package com.extracme.nevmp.service.qualification.impl;

import com.alibaba.fastjson.JSON;
import com.extracme.nevmp.api.BaseResponse;
import com.extracme.nevmp.api.ResultCode;
import com.extracme.nevmp.config.SpringContextUtil;
import com.extracme.nevmp.constant.ApiConst;
import com.extracme.nevmp.constant.Const;
import com.extracme.nevmp.converter.ConvertUtil;
import com.extracme.nevmp.dto.SaveOperateLogDTO;
import com.extracme.nevmp.dto.common.FileInfoDTO;
import com.extracme.nevmp.dto.common.PageInfoBO;
import com.extracme.nevmp.dto.exchange.*;
import com.extracme.nevmp.dto.library.*;
import com.extracme.nevmp.dto.owner.*;
import com.extracme.nevmp.dto.qualification.owner.*;
import com.extracme.nevmp.enums.*;
import com.extracme.nevmp.error.ServiceException;
import com.extracme.nevmp.mapper.*;
import com.extracme.nevmp.mapper.extend.OwnerQualificationExtendMapper;
import com.extracme.nevmp.model.*;
import com.extracme.nevmp.service.FileService;
import com.extracme.nevmp.service.OwnerTrafficQualificationService;
import com.extracme.nevmp.service.UserOperateLogService;
import com.extracme.nevmp.service.async.AsyncService;
import com.extracme.nevmp.service.library.LibraryService;
import com.extracme.nevmp.service.qualification.*;
import com.extracme.nevmp.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.dynamic.sql.SqlBuilder;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Nullable;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.extracme.nevmp.mapper.OwnerIdentityInfoDynamicSqlSupport.ownerIdentityInfo;
import static com.extracme.nevmp.mapper.OwnerIdentityInfoDynamicSqlSupport.status;
import static com.extracme.nevmp.mapper.OwnerQualificationDynamicSqlSupport.ownerQualification;
import static com.extracme.nevmp.mapper.OwnerQualificationReviewDetailDynamicSqlSupport.ownerQualificationReviewDetail;
import static com.extracme.nevmp.mapper.OwnerResidencePermitPointQualificationDynamicSqlSupport.ownerResidencePermitPointQualification;
import static com.extracme.nevmp.mapper.OwnerTrafficQualificationDynamicSqlSupport.ownerTrafficQualification;
import static org.mybatis.dynamic.sql.SqlBuilder.*;

/**
 * 意向购车用户服务
 *
 * <AUTHOR>
 * @Description
 */
@Slf4j
@Service
public class OwnerQualificationServiceImpl implements OwnerQualificationService {

    Logger logger = LoggerFactory.getLogger(OwnerQualificationServiceImpl.class);

    @Autowired
    private OwnerQualificationExtendMapper ownerQualificationMapper;

    @Autowired
    private OwnerIdentityInfoMapper ownerIdentityInfoMapper;

    @Autowired
    private OwnerQualificationReviewDetailMapper ownerQualificationReviewDetailMapper;

    @Autowired
    private OnethingApplyInfoMapper onethingApplyInfoMapper;

    @Autowired
    private OwnerTrafficCommitteeQualificationMapper ownerTrafficCommitteeQualificationMapper;

    @Autowired
    private OwnerTrafficCommitteeExchangeMapper ownerTrafficCommitteeExchangeMapper;

    @Autowired
    private OwnerTrafficQualificationService ownerTrafficQualificationService;

    @Autowired
    private OwnerQualificationSpecialService ownerQualificationSpecialService;

    @Autowired
    private FileService fileService;

    @Autowired
    private SocialService socialService;

    @Autowired
    private NewSocialService newSocialService;

    @Autowired
    private CreditService creditService;

    @Autowired
    private DriverService driverService;

    @Autowired
    private ResidencePermitService residencePermitService;

    @Autowired
    private TrafficCommitteeService trafficCommitteeService;

    @Autowired
    private LibraryService libraryService;

    @Autowired
    private UserOperateLogService userOperateLogService;

    @Autowired
    private HouseholdRegistrationService householdRegistrationService;

    @Autowired
    private AsyncService asyncService;

    @Autowired
    private OwnerResidencePermitPointQualificationMapper ownerResidencePermitPointQualificationMapper;

    public static final String MESSAGE_CONTENT_2024 = "经查询，您的新能源汽车专用牌照申领一件事“购车资格查询”事项已通过，" +
            "符合本轮《上海市鼓励购买和使用新能源汽车实施办法》专用牌照额度的申领条件，有效期至2024年12月31日。下一步为新能源汽车专用牌照申领一件事“车辆信息确认”事项，" +
            "请您与拟购车辆所属的新能源汽车生产厂 商做好沟通，待新能源汽车生产厂商上传相关信息后，通过“一网通办”签署上海市购买和使用新能源汽车安全承诺书。";

    public static final String MESSAGE_CONTENT = "经查询，您的新能源汽车专用牌照申领一件事“购车资格查询”事项已通过，" +
            "符合本轮《上海市鼓励购买和使用新能源汽车实施办法》专用牌照额度的申领条件，有效期为90天。下一步为新能源汽车专用牌照申领一件事“车辆信息确认”事项，" +
            "请您与拟购车辆所属的新能源汽车生产厂 商做好沟通，待新能源汽车生产厂商上传相关信息后，通过“一网通办”签署上海市购买和使用新能源汽车安全承诺书。";


    @Override
    public PageInfoBO<OwnerQualification> searchOwnerQualification(SearchOwnerQualificationDTO searchOwnerQualificationDTO) {
        if(StringUtils.isNotBlank(searchOwnerQualificationDTO.getNameLike())){
            searchOwnerQualificationDTO.setNameLike("%" + searchOwnerQualificationDTO.getNameLike() + "%");
        }
        //按照查询条件查询意向用户列表
        SelectStatementProvider selectOwner = SqlBuilder.select(ownerQualification.allColumns())
                .from(ownerQualification)
                .where()
                .and(ownerQualification.property, isEqualToWhenPresent(searchOwnerQualificationDTO.getProperty()))
                .and(ownerQualification.authType, isEqualToWhenPresent(searchOwnerQualificationDTO.getAuthType()))
                .and(ownerQualification.authId, isInWhenPresent(searchOwnerQualificationDTO.getAuthId()))
                .and(ownerQualification.applyStatus, isEqualToWhenPresent(searchOwnerQualificationDTO.getApplyStatus()))
                .and(ownerQualification.name, isEqualToWhenPresent(searchOwnerQualificationDTO.getName()))
                .and(ownerQualification.name, isLikeWhenPresent(searchOwnerQualificationDTO.getNameLike()))
                .and(ownerQualification.householdRegistrationType, isEqualToWhenPresent(searchOwnerQualificationDTO.getHouseholdRegistrationType()))
                .and(ownerQualification.applyTime, isGreaterThanOrEqualToWhenPresent(searchOwnerQualificationDTO.getApplyTimeStart()))
                .and(ownerQualification.applyTime, isLessThanWhenPresent(searchOwnerQualificationDTO.getApplyTimeEnd()))
                .and(ownerQualification.reviewTime, isGreaterThanOrEqualToWhenPresent(searchOwnerQualificationDTO.getReviewTimeStart()))
                .and(ownerQualification.reviewTime, isLessThanWhenPresent(searchOwnerQualificationDTO.getReviewTimeEnd()))
                .and(ownerQualification.exchangeStatus, isEqualToWhenPresent(searchOwnerQualificationDTO.getExchangeStatus()))
                .and(ownerQualification.exchangeApplyTime, isGreaterThanOrEqualToWhenPresent(searchOwnerQualificationDTO.getExchangeApplyTimeStart()))
                .and(ownerQualification.exchangeApplyTime, isLessThanWhenPresent(searchOwnerQualificationDTO.getExchangeApplyTimeEnd()))
                .and(ownerQualification.exchangeReviewTime, isGreaterThanOrEqualToWhenPresent(searchOwnerQualificationDTO.getExchangeReviewTimeStart()))
                .and(ownerQualification.exchangeReviewTime, isLessThanWhenPresent(searchOwnerQualificationDTO.getExchangeReviewTimeEnd()))
                .and(ownerQualification.status, isEqualTo(1))
                .and(ownerQualification.qualificationType, isEqualTo(searchOwnerQualificationDTO.getQualificationType()))
                .orderBy(ownerQualification.id)
                .limit(searchOwnerQualificationDTO.getPageSize())
                .offset((searchOwnerQualificationDTO.getPageNum() - 1) * searchOwnerQualificationDTO.getPageSize())
                .build()
                .render(RenderingStrategies.MYBATIS3);
        List<OwnerQualification> ownerQualificationList = ownerQualificationMapper.selectMany(selectOwner);

        //查询总条数
        SelectStatementProvider countSql = countFrom(ownerQualification)
                .where()
                .and(ownerQualification.property, isEqualToWhenPresent(searchOwnerQualificationDTO.getProperty()))
                .and(ownerQualification.authType, isEqualToWhenPresent(searchOwnerQualificationDTO.getAuthType()))
                .and(ownerQualification.authId, isInWhenPresent(searchOwnerQualificationDTO.getAuthId()))
                .and(ownerQualification.applyStatus, isEqualToWhenPresent(searchOwnerQualificationDTO.getApplyStatus()))
                .and(ownerQualification.name, isEqualToWhenPresent(searchOwnerQualificationDTO.getName()))
                .and(ownerQualification.name, isLikeWhenPresent(searchOwnerQualificationDTO.getNameLike()))
                .and(ownerQualification.householdRegistrationType, isEqualToWhenPresent(searchOwnerQualificationDTO.getHouseholdRegistrationType()))
                .and(ownerQualification.applyTime, isGreaterThanOrEqualToWhenPresent(searchOwnerQualificationDTO.getApplyTimeStart()))
                .and(ownerQualification.applyTime, isLessThanWhenPresent(searchOwnerQualificationDTO.getApplyTimeEnd()))
                .and(ownerQualification.reviewTime, isGreaterThanOrEqualToWhenPresent(searchOwnerQualificationDTO.getReviewTimeStart()))
                .and(ownerQualification.reviewTime, isLessThanWhenPresent(searchOwnerQualificationDTO.getReviewTimeEnd()))
                .and(ownerQualification.exchangeStatus, isEqualToWhenPresent(searchOwnerQualificationDTO.getExchangeStatus()))
                .and(ownerQualification.exchangeApplyTime, isGreaterThanOrEqualToWhenPresent(searchOwnerQualificationDTO.getExchangeApplyTimeStart()))
                .and(ownerQualification.exchangeApplyTime, isLessThanWhenPresent(searchOwnerQualificationDTO.getExchangeApplyTimeEnd()))
                .and(ownerQualification.exchangeReviewTime, isGreaterThanOrEqualToWhenPresent(searchOwnerQualificationDTO.getExchangeReviewTimeStart()))
                .and(ownerQualification.exchangeReviewTime, isLessThanWhenPresent(searchOwnerQualificationDTO.getExchangeReviewTimeEnd()))
                .and(ownerQualification.status, isEqualTo(1))
                .and(ownerQualification.qualificationType, isEqualTo(searchOwnerQualificationDTO.getQualificationType()))
                .build().render(RenderingStrategies.MYBATIS3);
        Long count = ownerQualificationMapper.count(countSql);

        return new PageInfoBO<>(count, ownerQualificationList);
    }

    @Override
    public OwnerQualificationDetailDTO getOwnerQualificationDetail(Long id) {
        //查询意向用户基础信息
        Optional<OwnerQualification> optionalOwnerQualification = ownerQualificationMapper.selectByPrimaryKey(id);
        if (!optionalOwnerQualification.isPresent()) {
            throw new ServiceException("未查询到相应用户信息");
        }
        return getOwnerQualificationDetail(optionalOwnerQualification);
    }

    @Override
    public OwnerQualification getOwnerQualificationById(Long id) {
        if(id == null){
            return null;
        }
        return ownerQualificationMapper.selectByPrimaryKey(id).orElse(null);
    }


    //TODO 后期重写
    private OwnerQualificationDetailDTO getOwnerQualificationDetail(Optional<OwnerQualification> optionalOwnerQualification) {
        OwnerQualificationDetailDTO ownerQualificationDetailDTO = ConvertUtil.normalConvert(optionalOwnerQualification.get(), OwnerQualificationDetailDTO.class);
        ownerQualificationDetailDTO.setReviewStatus(optionalOwnerQualification.get().getApplyStatus());

        // 查看用户驾照信息
        OwnerTrafficQualification ownerTrafficQualification = ownerTrafficQualificationService.queryOwnerTrafficQualification(optionalOwnerQualification.get().getId());
        if (ownerTrafficQualification != null) {
            ownerQualificationDetailDTO.setDriverLicenseCode(ownerTrafficQualification.getDriverLicenseCode());
            ownerQualificationDetailDTO.setDriverLicenseIssuingPlace(ownerTrafficQualification.getDriverLicenseIssuingPlace());
            ownerQualificationDetailDTO.setDriverLicenseIssuingOrganization(ownerTrafficQualification.getDriverLicenseIssuingOrganization());
            ownerQualificationDetailDTO.setDriverFileNo(ownerTrafficQualification.getDriverFileNo());
        }

        //用户资格证明材料
        List<FileInfoDTO> reconsiderationFile = fileService.getFileInfo(FileTypeEnum.QUALIFICATION_PROOF_COPY, optionalOwnerQualification.get().getId());
        ownerQualificationDetailDTO.setReconsiderationFileCopy(reconsiderationFile);

        //军官证明材料
        List<FileInfoDTO> militaryLicenseFile = fileService.getFileInfo(FileTypeEnum.MILITARY_OFFICER_COPY, optionalOwnerQualification.get().getId());
        ownerQualificationDetailDTO.setMilitaryLicenseFile(militaryLicenseFile);

        //以旧换新，查询以旧换新证明材料
        if(Objects.equals(OwnerQualificationTypeEnum.EXCHANGE.getType(),optionalOwnerQualification.get().getQualificationType())){
            List<FileInfoDTO> exchangeProofFile = fileService.getFileInfo(FileTypeEnum.EXCHANGE_PROOF_COPY, optionalOwnerQualification.get().getId());
            ownerQualificationDetailDTO.setExchangeProofFile(exchangeProofFile);
        }

        //核查信息
        SelectStatementProvider selectSql = select(ownerQualificationReviewDetail.allColumns())
                .from(ownerQualificationReviewDetail)
                .where(ownerQualificationReviewDetail.ownerQulificationId, isEqualTo(optionalOwnerQualification.get().getId()))
                .build().render(RenderingStrategies.MYBATIS3);
        List<OwnerQualificationReviewDetail> ownerQualificationReviewDetailList = ownerQualificationReviewDetailMapper.selectMany(selectSql);

        // 查看居住证是否满120分
        SelectStatementProvider selectStatement = SqlBuilder.select(ownerResidencePermitPointQualification.allColumns())
                .from(ownerResidencePermitPointQualification)
                .where()
                .and(ownerResidencePermitPointQualification.ownerQualificationId, isEqualTo(ownerQualificationDetailDTO.getId()))
                .and(ownerResidencePermitPointQualification.status, isEqualTo(1))
                .limit(1)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        Optional<OwnerResidencePermitPointQualification> ownerResidencePermitPointQualification = ownerResidencePermitPointQualificationMapper.selectOne(selectStatement);
        if (ownerResidencePermitPointQualification.isPresent() && Integer.valueOf(1).equals(ownerResidencePermitPointQualification.get().getHasEnoughPoint())) {
            ownerQualificationDetailDTO.setHasEnoughPoint(1);
        } else if (ownerResidencePermitPointQualification.isPresent() && Integer.valueOf(0).equals(ownerResidencePermitPointQualification.get().getHasEnoughPoint())) {
            ownerQualificationDetailDTO.setHasEnoughPoint(0);
        }
        ownerQualificationReviewDetailList.forEach(
                reviewDetail -> Optional.ofNullable(reviewDetail.getReviewType()).ifPresent(
                        type -> {
                            BaseOwnerQualificationReviewDetail baseOwnerQualificationReviewDetail = ConvertUtil.normalConvert(reviewDetail, BaseOwnerQualificationReviewDetail.class);
                            switch (type) {
                                //公安-驾照
                                case 0:
                                    ownerQualificationDetailDTO.setOwnerTrafficReviewDetail(baseOwnerQualificationReviewDetail);
                                    break;
                                //信用
                                case 1:
                                    ownerQualificationDetailDTO.setOwnerCreditReviewDetail(baseOwnerQualificationReviewDetail);

                                    //这一坨代码有问题，需要优化
                                    if (ownerQualificationDetailDTO.getOwnerCreditReviewDetail().getReviewStatus() == 3 && ownerQualificationDetailDTO.getAuthType() != 1 && ownerQualificationDetailDTO.getProperty() == 1) {
                                        if (ownerQualificationDetailDTO.getReconsiderDescribe() == null) {
                                            ownerQualificationDetailDTO.setReconsiderDescribe("信用相关材料");
                                        } else {
                                            ownerQualificationDetailDTO.setReconsiderDescribe(ownerQualificationDetailDTO.getReconsiderDescribe() + "|信用相关材料|");
                                        }
                                    }
                                    break;
                                //社保
                                case 2:
                                    ownerQualificationDetailDTO.setOwnerSocialInsuranceReviewDetail(baseOwnerQualificationReviewDetail);
                                    //私人
                                    if(ownerQualificationDetailDTO.getProperty() == 1){
                                        //审核拒绝 且 （居住证积分满足 或 外籍人士)
                                        if (ownerQualificationDetailDTO.getOwnerSocialInsuranceReviewDetail().getReviewStatus() == 2 && (Integer.valueOf(1).equals(ownerQualificationDetailDTO.getHasEnoughPoint()) || ownerQualificationDetailDTO.getAuthType() != 1)) {
                                            if (ownerQualificationDetailDTO.getReconsiderDescribe() == null) {
                                                ownerQualificationDetailDTO.setReconsiderDescribe("前6个月个税税单");
                                            } else {
                                                ownerQualificationDetailDTO.setReconsiderDescribe(ownerQualificationDetailDTO.getReconsiderDescribe() + "|前6个月个税税单|");
                                            }

                                            //审核拒绝
                                        } else if (ownerQualificationDetailDTO.getOwnerSocialInsuranceReviewDetail().getReviewStatus() == 2) {
                                            if (ownerQualificationDetailDTO.getReconsiderDescribe() == null) {
                                                ownerQualificationDetailDTO.setReconsiderDescribe("前36个月个税税单");
                                            } else {
                                                ownerQualificationDetailDTO.setReconsiderDescribe(ownerQualificationDetailDTO.getReconsiderDescribe() + "|前36个月个税税单|");
                                            }
                                        }
                                    }else if(ownerQualificationDetailDTO.getProperty() == 2){
                                        if (ownerQualificationDetailDTO.getOwnerSocialInsuranceReviewDetail().getReviewStatus() == 2) {
                                            if (ownerQualificationDetailDTO.getReconsiderDescribe() == null) {
                                                ownerQualificationDetailDTO.setReconsiderDescribe("前1年完税证明");
                                            } else {
                                                ownerQualificationDetailDTO.setReconsiderDescribe(ownerQualificationDetailDTO.getReconsiderDescribe() + "|前1年完税证明|");
                                            }
                                        }
                                    }

                                    break;
                                //居住证
                                case 3:
                                    ownerQualificationDetailDTO.setResidencePermitReviewDetail(baseOwnerQualificationReviewDetail);
                                    break;
                                //交通委-名下大牌额度
                                case 4:
                                    ownerQualificationDetailDTO.setNonOperationalReviewDetail(baseOwnerQualificationReviewDetail);
                                    break;
                                //交通委-以旧换新额度
                                case 5:
                                    ownerQualificationDetailDTO.setOwnerExchangeReviewDetail(baseOwnerQualificationReviewDetail);
                                    break;
                            }
                        }
                )
        );
        //确保当前在复核有效期之前
        if (optionalOwnerQualification.get().getExpireTime() != null && new Date().before(optionalOwnerQualification.get().getExpireTime()) &&
                OwnerQualificationReviewStatusEnum.DENY.getStatus().equals(ownerQualificationDetailDTO.getReviewStatus())) {
            ownerQualificationDetailDTO.setReconsiderationStatus(1);
        } else {
            ownerQualificationDetailDTO.setReconsiderationStatus(0);
        }

        // 判断能否信用复核
        ownerQualificationDetailDTO.setCanCreditReconsider(validCreditCanReconsider(optionalOwnerQualification.get(), ownerQualificationReviewDetailList));
        return ownerQualificationDetailDTO;
    }

    /**
     * 信用能否复核
     * @param ownerQualification
     * @return true:信用可复核 false:信用不可复核
     */
    private Boolean validCreditCanReconsider(OwnerQualification ownerQualification){
        SelectStatementProvider selectStatement = SqlBuilder.select(ownerQualificationReviewDetail.allColumns())
                .from(ownerQualificationReviewDetail)
                .where(ownerQualificationReviewDetail.ownerQulificationId, isEqualTo(ownerQualification.getId()))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        List<OwnerQualificationReviewDetail> ownerQualificationReviewDetailList = ownerQualificationReviewDetailMapper.selectMany(selectStatement);
        return validCreditCanReconsider(ownerQualification, ownerQualificationReviewDetailList);
    }

    /**
     * 信用能否复核
     * 按照正常流畅，信用不通过不能复核
     * 但是由于刑事案件的执行期内数据丢失，针对未在形式期内的用户 需要有渠道能够手动复核
     * @param ownerQualification
     * @param reviewDetailList
     * @return true:信用可复核 false:信用不可复核
     */
    private Boolean validCreditCanReconsider(OwnerQualification ownerQualification,List<OwnerQualificationReviewDetail> reviewDetailList){
        //校验审核拒绝日期不能超过当前时间30天
        if(ownerQualification.getReviewTime() == null || new Date().getTime() - ownerQualification.getReviewTime().getTime() > TimeUnit.DAYS.toMillis(30L)){
            return false;
        }

        //只有信用审核导致最终结果不通过的用户，允许进行复核
        if(OwnerQualificationStatusEnum.DENY.getStatus().equals(ownerQualification.getApplyStatus())){
            boolean result = true;
            for(OwnerQualificationReviewDetail reviewDetail : reviewDetailList){
                //信用审核信息
                if(reviewDetail.getReviewType() == 1){
                    //已审核通过的不能复核
                    if(OwnerQualificationReviewStatusEnum.APPROVE.getStatus().equals(reviewDetail.getReviewStatus())){
                        result = false;
                        break;
                    }
                }
                //其他
                else{
                    if(OwnerQualificationReviewStatusEnum.DENY.getStatus().equals(reviewDetail.getReviewStatus())){
                        result = false;
                        break;
                    }
                }
            }
            return  result;
        }else{
            return false;
        }
    }

    @Override
    public OwnerQualificationDetailDTO queryOwnerQualificationDetail(Integer authType, String authId) {
        //查询最近一条意向用户记录
        SelectStatementProvider selectStatement = select(ownerQualification.allColumns())
                .from(ownerQualification)
                .where()
                .and(ownerQualification.authId, isEqualTo(authId))
                .and(ownerQualification.authType, isEqualTo(authType))
                .and(ownerQualification.qualificationType, isEqualTo(OwnerQualificationTypeEnum.NORMAL.getType()))
                .and(ownerQualification.status, isEqualTo(1))
                .orderBy(ownerQualification.id.descending())
                .limit(1)
                .build().render(RenderingStrategies.MYBATIS3);
        Optional<OwnerQualification> optionalOwnerQualification = ownerQualificationMapper.selectOne(selectStatement);
        if (!optionalOwnerQualification.isPresent()) {
            throw new ServiceException("未查到相关用户信息:" + authId);
        }
        // 针对旧表的意向用户信息查询
        return getOwnerQualificationDetail(optionalOwnerQualification);
    }

    @Override
    public OwnerQualificationDetailDTO queryOwnerExchangeQualification(Integer authType, String authId, String exchangeVehicleNo) {
        //根据用户证件号及以旧换新车架号查询相关记录
        SelectStatementProvider selectStatement = select(ownerQualification.allColumns())
                .from(ownerQualification)
                .where()
                .and(ownerQualification.authId, isEqualTo(authId))
                .and(ownerQualification.authType, isEqualTo(authType))
                .and(ownerQualification.exchangeVehicleNo, isEqualTo(exchangeVehicleNo))
                .and(ownerQualification.qualificationType, isEqualTo(OwnerQualificationTypeEnum.EXCHANGE.getType()))
                .and(ownerQualification.status, isEqualTo(1))
                .orderBy(ownerQualification.id.descending())
                .limit(1)
                .build().render(RenderingStrategies.MYBATIS3);
        Optional<OwnerQualification> optionalOwnerQualification = ownerQualificationMapper.selectOne(selectStatement);
        if (!optionalOwnerQualification.isPresent()) {
            throw new ServiceException("未查到相关用户信息:" + authId);
        }
        // 针对旧表的意向用户信息查询
        return getOwnerQualificationDetail(optionalOwnerQualification);
    }

    @Override
    public String queryOwnerMobilePhone(int authType, String authId) {
        String result = StringUtils.EMPTY;
        //查询最近一条意向用户记录
        SelectStatementProvider selectStatement = select(ownerQualification.allColumns())
                .from(ownerQualification)
                .where()
                .and(ownerQualification.authId, isEqualTo(authId))
                .and(ownerQualification.authType, isEqualTo(authType))
                .and(ownerQualification.qualificationType, isEqualTo(OwnerQualificationTypeEnum.NORMAL.getType()))
                .and(ownerQualification.status,isEqualTo(1))
                .orderBy(ownerQualification.id.descending())
                .limit(1)
                .build().render(RenderingStrategies.MYBATIS3);
        OwnerQualification owner = ownerQualificationMapper.selectOne(selectStatement).orElse(null);
        if(owner != null){
            result = owner.getMobilePhone();
        }
        return result;
    }

    @Override
    public OwnerQualificationDetailHistoryDTO queryOwnerQualificationDetailHistory(String authType, String authId) {
        OwnerQualificationDetailHistoryDTO ownerQualificationDetailHistory = new OwnerQualificationDetailHistoryDTO();

        SelectStatementProvider selectStatement = SqlBuilder.select(ownerIdentityInfo.allColumns())
                .from(ownerIdentityInfo)
                .where(ownerIdentityInfo.authIdentityId, isEqualTo(authId))
                .and(ownerIdentityInfo.authIdentityKind, isEqualTo(authType))
                .and(ownerIdentityInfo.ownerIdApp, isNotEqualTo(0))
                .limit(1)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        final Optional<OwnerIdentityInfo> optionalOwnerIdentityInfo = ownerIdentityInfoMapper.selectOne(selectStatement);
        if (optionalOwnerIdentityInfo.isPresent()) {
            ownerQualificationDetailHistory.setAuthId(optionalOwnerIdentityInfo.get().getAuthIdentityId());
            ownerQualificationDetailHistory.setAuthKind(optionalOwnerIdentityInfo.get().getAuthIdentityKind());
            ownerQualificationDetailHistory.setExpireTime(optionalOwnerIdentityInfo.get().getExpireTime());
            ownerQualificationDetailHistory.setName(optionalOwnerIdentityInfo.get().getOwnerIdentityName());
            ownerQualificationDetailHistory.setStatus(optionalOwnerIdentityInfo.get().getStatus());
            ownerQualificationDetailHistory.setRemark(optionalOwnerIdentityInfo.get().getRemark());
        }
        return ownerQualificationDetailHistory;
    }

    @Override
    public BaseResponse reconsiderFile(ReconsiderFileDTO reconsiderFileDTO) {
        Optional<OwnerQualification> optionalOwnerQualification = ownerQualificationMapper.selectByPrimaryKey(reconsiderFileDTO.getId());
        if (!optionalOwnerQualification.isPresent()) {
            throw new ServiceException("未查询到相应用户信息");
        }
        if (!OwnerQualificationStatusEnum.DENY.getStatus().equals(optionalOwnerQualification.get().getApplyStatus())) {
            throw new ServiceException("当前状态不能进行该操作");
        }

        // 可以简化成针对状态+有效期的判断
        //复核条件判断
        if(optionalOwnerQualification.get().getExpireTime() == null || optionalOwnerQualification.get().getExpireTime().compareTo(new Date()) <= 0){
            throw new ServiceException("当前状态不能进行该操作");
        }

        // 判断是否有照片，是否之前复核过，只能复核一次
        List<FileInfoDTO> fileInfo = fileService.getFileInfo(FileTypeEnum.QUALIFICATION_PROOF_COPY, optionalOwnerQualification.get().getId());
        List<FileInfoDTO> militaryFile = fileService.getFileInfo(FileTypeEnum.MILITARY_OFFICER_COPY,optionalOwnerQualification.get().getId());


        if (!fileInfo.isEmpty() || !militaryFile.isEmpty()) {
            throw new ServiceException("该记录已复核过，不可多次复核，请重新创建并提交申请");
        }

        //清空之前上传的图片
        fileService.clearFile(FileTypeEnum.QUALIFICATION_PROOF_COPY, optionalOwnerQualification.get().getId(), reconsiderFileDTO.getOperatorId(), reconsiderFileDTO.getOperatorName());
        //保存上传的文件
        List<String> collect = reconsiderFileDTO.getReconsiderationFileCopy().stream().map(FileInfoDTO::getRelativeFilePath).collect(Collectors.toList());
        fileService.saveFileInfo(FileTypeEnum.QUALIFICATION_PROOF_COPY, collect, reconsiderFileDTO.getId(),
                reconsiderFileDTO.getOperatorId(), reconsiderFileDTO.getOperatorName());


        //将办件状态更新为复核
        reconsiderQualification(optionalOwnerQualification.get().getId(), reconsiderFileDTO.getOperatorId()
                , reconsiderFileDTO.getOperatorName(), reconsiderFileDTO.getReconsiderOrgName());
        if (StringUtils.isNotBlank(optionalOwnerQualification.get().getApplyNo())) {
            //受理办件
            libraryService.acceptApply(optionalOwnerQualification.get().getApplyNo());
            //更新指南
            libraryService.changeStatus(optionalOwnerQualification.get().getApplyNo(), AnnouncementEnum.QUALIFICATION_ACCEPTED);
        }
        return new BaseResponse();
    }

    @Override
    @Deprecated
    public BaseResponse reconsiderCreditQualification(ReconsiderFileDTO reconsiderFileDTO) {
        Optional<OwnerQualification> optionalOwnerQualification = ownerQualificationMapper.selectByPrimaryKey(reconsiderFileDTO.getId());
        if (!optionalOwnerQualification.isPresent()) {
            throw new ServiceException("未查询到相应用户信息");
        }
        //复核条件判断
        boolean  canCreditReconsider = validCreditCanReconsider(optionalOwnerQualification.get());
        if (!canCreditReconsider) {
            throw new ServiceException("当前状态不能进行信用复核");
        }

        //清空之前上传的图片
        fileService.clearFile(FileTypeEnum.QUALIFICATION_PROOF_COPY, optionalOwnerQualification.get().getId(), reconsiderFileDTO.getOperatorId(), reconsiderFileDTO.getOperatorName());
        //保存上传的文件
        List<String> collect = reconsiderFileDTO.getReconsiderationFileCopy().stream().map(FileInfoDTO::getRelativeFilePath).collect(Collectors.toList());
        fileService.saveFileInfo(FileTypeEnum.QUALIFICATION_PROOF_COPY, collect, reconsiderFileDTO.getId(),
                reconsiderFileDTO.getOperatorId(), reconsiderFileDTO.getOperatorName());

        //修复信用资质
        recoverCreditQualification(optionalOwnerQualification.get().getId(), reconsiderFileDTO.getOperatorId(), reconsiderFileDTO.getOperatorName());

        //发送短信通知
        String content;
        if(new Date().before(DateUtil.parse("2025-01-01 00:00:00", DateUtil.DATE_TYPE1))){
            content = MESSAGE_CONTENT_2024;
        } else {
            content = MESSAGE_CONTENT;
        }
        MessageUtil.sendMessage(optionalOwnerQualification.get().getMobilePhone(), content);
        return new BaseResponse();
    }

    private void recoverCreditQualification(Long ownerQualificationId, String operatorId, String operatorName) {

        Date now = new Date();
        //复核意向用户
        UpdateStatementProvider updateStatement = SqlBuilder.update(ownerQualification)
                .set(ownerQualification.applyStatus).equalTo(OwnerQualificationStatusEnum.APPROVE.getStatus())
                .set(ownerQualification.reviewTime).equalTo(now)
                .set(ownerQualification.expireTime).equalTo(new Date(TimeUnit.DAYS.toMillis(90) + now.getTime()))
                .set(ownerQualification.updatedTime).equalTo(new Date())
                .set(ownerQualification.updatedUserId).equalTo(operatorId)
                .set(ownerQualification.updatedUserName).equalTo(operatorName)
                .where(ownerQualification.id, isEqualTo(ownerQualificationId))
                .and(ownerQualification.applyStatus, isEqualTo(OwnerQualificationStatusEnum.DENY.getStatus()))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        ownerQualificationMapper.update(updateStatement);

        //保存操作日志
        SaveOperateLogDTO saveOperateLogDTO = SaveOperateLogDTO.builder()
                .userOperateTypeEnum(UserOperateTypeEnum.RECONSIDER_OWNER_CREDIT_QUALIFICATION)
                .ownerQualificationId(ownerQualificationId)
                .operateUserId(operatorId)
                .operateUserName(operatorName)
                .build();
        userOperateLogService.saveOperateLog(saveOperateLogDTO);
    }

    @Override
    public BaseResponse approveOwnerQualification(ApproveOwnerQualificationDTO approveOwnerQualificationDTO) {
        Optional<OwnerQualification> optionalOwnerQualification = ownerQualificationMapper.selectByPrimaryKey(approveOwnerQualificationDTO.getId());
        if (!optionalOwnerQualification.isPresent()) {
            throw new ServiceException("未查询到相应的用户信息");
        }
        if (!OwnerQualificationStatusEnum.RECONSIDERATION.getStatus().equals(optionalOwnerQualification.get().getApplyStatus())) {
            throw new ServiceException("当前状态不能进行该操作");
        }

        //更新意向用户状态
        Date now = new Date();
        OwnerQualification approveOwnerQualification = new OwnerQualification();
        approveOwnerQualification.setId(optionalOwnerQualification.get().getId());
        approveOwnerQualification.setApplyStatus(OwnerQualificationStatusEnum.APPROVE.getStatus());
        approveOwnerQualification.setReviewTime(now);
        approveOwnerQualification.setReason("");
        //有效期，需90天内绑定
        approveOwnerQualification.setExpireTime(new Date(TimeUnit.DAYS.toMillis(90) + now.getTime()));
        approveOwnerQualification.setUpdatedTime(new Date());
        approveOwnerQualification.setUpdatedUserId(approveOwnerQualificationDTO.getOperatorId());
        approveOwnerQualification.setUpdatedUserName(approveOwnerQualificationDTO.getOperatorName());
        ownerQualificationMapper.updateByPrimaryKeySelective(approveOwnerQualification);

        // 私人需判断购买的是插电式混动车还是非插电式混动车
        String content = StringUtils.EMPTY;
        if(new Date().before(DateUtil.parse("2025-01-01 00:00:00", DateUtil.DATE_TYPE1))){
            content = MESSAGE_CONTENT_2024;
        } else {
            content = MESSAGE_CONTENT;
        }
        MessageUtil.sendMessage(approveOwnerQualification.getMobilePhone(), content);


        //保存操作日志
        SaveOperateLogDTO saveOperateLogDTO = SaveOperateLogDTO.builder()
                .userOperateTypeEnum(UserOperateTypeEnum.APPROVE_OWNER_QUALIFICATION)
                .operateUserName(approveOwnerQualificationDTO.getOperatorName())
                .vehicleId(approveOwnerQualificationDTO.getId())
                .operateUserId(approveOwnerQualificationDTO.getOperatorId())

                .build();
        userOperateLogService.saveOperateLog(saveOperateLogDTO);

        //更新办件库信息
        if (StringUtils.isNotBlank(optionalOwnerQualification.get().getApplyNo())) {
            asyncService.approveOwnerQualification(optionalOwnerQualification.get().getApplyNo());
        }
        return new BaseResponse();
    }

    @Override
    public BaseResponse denyOwnerQualification(DenyOwnerQualificationDTO denyOwnerQualificationDTO) {
        Optional<OwnerQualification> optionalOwnerQualification = ownerQualificationMapper.selectByPrimaryKey(denyOwnerQualificationDTO.getId());
        if (!optionalOwnerQualification.isPresent()) {
            throw new ServiceException("未查询到相应用户信息");
        }
        if (!OwnerQualificationStatusEnum.RECONSIDERATION.getStatus().equals(optionalOwnerQualification.get().getApplyStatus())) {
            throw new ServiceException("当前状态不能进行该操作");
        }

        //更新用户资格信息
        Date now = new Date();
        UpdateStatementProvider updateStatement = SqlBuilder.update(ownerQualification)
                .set(ownerQualification.applyStatus).equalTo(OwnerQualificationStatusEnum.DENY.getStatus())
                .set(ownerQualification.reviewTime).equalTo(now)
                .set(ownerQualification.reason).equalTo(denyOwnerQualificationDTO.getReason())
                //注:此处需要将过期时间置空，来区分是允许补正的还是直接拒绝的
                .set(ownerQualification.expireTime).equalToNull()
                .set(ownerQualification.updatedTime).equalTo(now)
                .set(ownerQualification.updatedUserId).equalTo(denyOwnerQualificationDTO.getOperatorId())
                .set(ownerQualification.updatedUserName).equalTo(denyOwnerQualificationDTO.getOperatorName())
                .where(ownerQualification.id, isEqualTo(denyOwnerQualificationDTO.getId()))
                .and(ownerQualification.applyStatus, isEqualTo(OwnerQualificationStatusEnum.RECONSIDERATION.getStatus()))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        ownerQualificationMapper.update(updateStatement);

        String content = "经查询，您的新能源购车资格申请未通过，暂不符合新能源汽车专用牌照申领条件，原因为：" + denyOwnerQualificationDTO.getReason() +";如有其他问题，可于工作日上午9:00-11:30，下午1:30-5:00咨询021-58812035。";
        MessageUtil.sendMessage(optionalOwnerQualification.get().getMobilePhone(), content);

        //保存操作日志
        SaveOperateLogDTO saveOperateLogDTO = SaveOperateLogDTO.builder()
                .userOperateTypeEnum(UserOperateTypeEnum.DENY_OWNER_QUALIFICATION)
                .reason(denyOwnerQualificationDTO.getReason())
                .vehicleId(denyOwnerQualificationDTO.getId())
                .operateUserId(denyOwnerQualificationDTO.getOperatorId())
                .operateUserName(denyOwnerQualificationDTO.getOperatorName())
                .build();
        userOperateLogService.saveOperateLog(saveOperateLogDTO);

        //更新办件库信息
        if (StringUtils.isNotBlank(optionalOwnerQualification.get().getApplyNo())) {
            asyncService.denyOwnerQualification(optionalOwnerQualification.get().getApplyNo(), denyOwnerQualificationDTO.getReason());
        }
        return new BaseResponse();
    }

    /**
     * 发短信测试
     */
    @Override
    public void sendMessageTest(String mobilephone) {
//        Optional<OwnerQualification> optionalOwnerQualification = ownerQualificationMapper.selectByPrimaryKey(id);
        String content = "您的购车资格申请核查数据有误，已帮您撤回，请重新提交申请";
        System.out.println("发送短信");
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json");
        header.put("appkey","6YCa5Yqe5rWL6K+V");

        Map<String, String> params = new HashMap<>();
        params.put("phone",mobilephone);
        params.put("content",content);

        String response = RestTemplateUtil.post(ApiConst.Common.SEND_MESSAGE, header, params, String.class);
        logger.info("发送短信："+ mobilephone +",内容：" + content +" 调用结果:"+ response);
    }

    @Override
    public BaseResponse denyOwnerQualificationManualTest(DenyOwnerQualificationDTO denyOwnerQualificationDTO) {
        Optional<OwnerQualification> optionalOwnerQualification = ownerQualificationMapper.selectByPrimaryKey(denyOwnerQualificationDTO.getId());
        if (!optionalOwnerQualification.isPresent()) {
            throw new ServiceException("未查询到相应用户信息");
        }
        System.out.println("optionalOwnerQualification-----------------" + JSON.toJSONString(optionalOwnerQualification.get()));
        //更新用户资格信息
        Date now = new Date();
        UpdateStatementProvider updateStatement = SqlBuilder.update(ownerQualification)
                .set(ownerQualification.applyStatus).equalTo(OwnerQualificationStatusEnum.DENY.getStatus())
                .set(ownerQualification.reviewTime).equalTo(now)
                .set(ownerQualification.reason).equalTo(denyOwnerQualificationDTO.getReason())
                //注:此处需要将过期时间置空，来区分是允许补正的还是直接拒绝的
                .set(ownerQualification.expireTime).equalToNull()
                .set(ownerQualification.updatedTime).equalTo(now)
                .set(ownerQualification.updatedUserId).equalTo(denyOwnerQualificationDTO.getOperatorId())
                .set(ownerQualification.updatedUserName).equalTo(denyOwnerQualificationDTO.getOperatorName())
                .where(ownerQualification.id, isEqualTo(denyOwnerQualificationDTO.getId()))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        ownerQualificationMapper.update(updateStatement);

        String content = "您好，《上海市鼓励购买和使用新能源汽车实施办法》（沪府办规〔2021〕3号）已于3月1日实施，因购车资格的要求有调整，请重新提交“购车资格查询”事项。";
        System.out.println("发送短信");
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json");
        header.put("appkey","6YCa5Yqe5rWL6K+V");

        Map<String, String> params = new HashMap<>();
        params.put("phone",optionalOwnerQualification.get().getMobilePhone());
        params.put("content",content);

        String response = RestTemplateUtil.post(ApiConst.Common.SEND_MESSAGE, header, params, String.class);
        logger.info("发送短信："+ optionalOwnerQualification.get().getMobilePhone() +",内容：" + content +" 调用结果:"+ response);
        //保存操作日志
        SaveOperateLogDTO saveOperateLogDTO = SaveOperateLogDTO.builder()
                .userOperateTypeEnum(UserOperateTypeEnum.DENY_OWNER_QUALIFICATION)
                .reason(denyOwnerQualificationDTO.getReason())
                .vehicleId(denyOwnerQualificationDTO.getId())
                .operateUserId(denyOwnerQualificationDTO.getOperatorId())
                .operateUserName(denyOwnerQualificationDTO.getOperatorName())
                .build();
        userOperateLogService.saveOperateLog(saveOperateLogDTO);
        //更新办件库信息
        if (StringUtils.isNotBlank(optionalOwnerQualification.get().getApplyNo())) {
            libraryService.denyApply(optionalOwnerQualification.get().getApplyNo(), denyOwnerQualificationDTO.getReason());
        }
        return new BaseResponse();
    }

    @Override
    @Transactional
    public BaseResponse savePrivateOwnerQualification(SavePrivateOwnerQualificationDTO savePrivateOwnerQualificationDTO) {
        if (existUnfinishedQualification(savePrivateOwnerQualificationDTO.getAuthType(), savePrivateOwnerQualificationDTO.getAuthId())) {
            throw new ServiceException("您已提交过申请，无法重复提交");
        }

        //如果用户选择上海户籍，则需要校验是否符合
        if(savePrivateOwnerQualificationDTO.getHouseholdRegistrationType() == 0){
            boolean isLocalHousehold = householdRegistrationService.isLocalHousehold(savePrivateOwnerQualificationDTO.getName(), savePrivateOwnerQualificationDTO.getAuthId());
            if(!isLocalHousehold){
                log.error("用户：{}，户籍信息填写异常", savePrivateOwnerQualificationDTO.getAuthId());
                throw new ServiceException("请确认户籍信息是否填写正确");
            }
        }else if(savePrivateOwnerQualificationDTO.getHouseholdRegistrationType() == 1){
            //校验是否拥有居住证
            boolean hasResidencePermit = residencePermitService.hasResidencePermit(savePrivateOwnerQualificationDTO.getName(), savePrivateOwnerQualificationDTO.getAuthId());
            if(!hasResidencePermit){
                throw new ServiceException("外省市（非港澳台及其他外籍人士）必须持有有效上海市居住证才可办理");
            }
        }

        //保存意向用户信息
        OwnerQualification saveOwnerQualification = new OwnerQualification();
        saveOwnerQualification.setName(savePrivateOwnerQualificationDTO.getName());
        saveOwnerQualification.setAuthId(savePrivateOwnerQualificationDTO.getAuthId());
        saveOwnerQualification.setAuthType(savePrivateOwnerQualificationDTO.getAuthType());
        saveOwnerQualification.setHouseholdRegistrationType(savePrivateOwnerQualificationDTO.getHouseholdRegistrationType());
        saveOwnerQualification.setProperty(1);
        saveOwnerQualification.setIsMilitaryOfficer(0);
        saveOwnerQualification.setVehicleModelType(savePrivateOwnerQualificationDTO.getVehicleModelType());
        this.saveOwnerQualificationInfo(saveOwnerQualification, savePrivateOwnerQualificationDTO.getCreatedUserId(), savePrivateOwnerQualificationDTO.getCreatedUserName());

        //保存提交并受理办件
        DriverLicenseDTO driverLicense = DriverLicenseDTO.builder()
                .driverLicenseCode(savePrivateOwnerQualificationDTO.getDriverLicenseCode())
                .driverLicenseIssuingOrganization(savePrivateOwnerQualificationDTO.getDriverLicenseIssuingOrganization())
                .driverLicenseIssuingPlace(savePrivateOwnerQualificationDTO.getDriverLicenseIssuingPlace())
                .driverFileNo(savePrivateOwnerQualificationDTO.getDriverFileNo())
                .build();
        //自动分配核查信息
        autoAssignQualificationReview(saveOwnerQualification, driverLicense);
        return new BaseResponse();
    }

    @Override
    @Transactional
    public BaseResponse saveBusinessOwnerQualification(SaveBusinessOwnerQualificationDTO saveBusinessOwnerQualificationDTO) {
        Integer authType = AuthTypeEnum.CERTIFICATE_FOR_UNIFORM_SOCIAL_CREDIT_CODE.getType();
        if (existUnfinishedQualification(authType, saveBusinessOwnerQualificationDTO.getAuthId())) {
            throw new ServiceException("您已提交过申请，无法重复提交");
        }
        //保存意向用户信息
        OwnerQualification saveOwnerQualification = new OwnerQualification();
        saveOwnerQualification.setName(saveBusinessOwnerQualificationDTO.getName());
        saveOwnerQualification.setAuthType(authType);
        saveOwnerQualification.setAuthId(saveBusinessOwnerQualificationDTO.getAuthId());
        saveOwnerQualification.setProperty(2);
        saveOwnerQualification.setIsMilitaryOfficer(0);
        this.saveOwnerQualificationInfo(saveOwnerQualification, saveBusinessOwnerQualificationDTO.getCreatedUserId(), saveBusinessOwnerQualificationDTO.getCreatedUserName());
        //自动分配核查信息
        autoAssignQualificationReview(saveOwnerQualification);
        return new BaseResponse();
    }

    @Override
    @Transactional
    public BaseResponse saveForeignerQualificationInfo(SaveForeignerQualificationInfoDTO saveForeignerQualificationInfoDTO) {
        if (existUnfinishedQualification(saveForeignerQualificationInfoDTO.getAuthType(), saveForeignerQualificationInfoDTO.getAuthId())) {
            throw new ServiceException("您已提交过申请，无法重复提交");
        }
        //证件类型校验
        if (!Const.FOREIGNER_AUTH_TYPE.containsKey(saveForeignerQualificationInfoDTO.getAuthType())) {
            throw new ServiceException("该证件类型非港澳台或外籍证件，请在一网通办申请");
        }

        //保存意向用户信息
        OwnerQualification saveOwnerQualification = ConvertUtil.convert(saveForeignerQualificationInfoDTO, OwnerQualification.class);
        saveOwnerQualification.setIsMilitaryOfficer(0);
        saveOwnerQualification.setProperty(1);
        saveOwnerQualification.setHouseholdRegistrationType(2);
        saveOwnerQualification.setReconsiderationImg(saveForeignerQualificationInfoDTO.getCreatedUserOrgName());
        this.saveOwnerQualificationInfo(saveOwnerQualification, saveForeignerQualificationInfoDTO.getCreatedUserId(), saveForeignerQualificationInfoDTO.getCreatedUserName());

        //清除历史上传的扫描件
        fileService.clearFile(FileTypeEnum.QUALIFICATION_PROOF_COPY, saveOwnerQualification.getId(), saveForeignerQualificationInfoDTO.getCreatedUserId(), saveForeignerQualificationInfoDTO.getCreatedUserName());
        // 保存用户资格证明材料照扫描件信息
        fileService.saveFileInfo(FileTypeEnum.QUALIFICATION_PROOF_COPY, saveForeignerQualificationInfoDTO.getFile(), saveOwnerQualification.getId()
                , saveForeignerQualificationInfoDTO.getCreatedUserId(), saveForeignerQualificationInfoDTO.getCreatedUserName());

        //保存提交并受理办件
        DriverLicenseDTO driverLicense = DriverLicenseDTO.builder()
                .driverFileNo(saveForeignerQualificationInfoDTO.getDriverFileNo())
                .driverLicenseIssuingOrganization(saveForeignerQualificationInfoDTO.getDriverLicenseIssuingOrganization())
                .driverLicenseIssuingPlace(saveForeignerQualificationInfoDTO.getDriverLicenseIssuingPlace())
                .build();
        //自动分配核查信息
        autoAssignQualificationReview(saveOwnerQualification, driverLicense);
        return new BaseResponse();
    }

    @Override
    public BaseResponse saveMilitaryQualificationInfo(SaveMilitaryQualificationInfoDTO saveMilitaryQualificationInfoDTO) {
        if (existUnfinishedQualification(saveMilitaryQualificationInfoDTO.getAuthType(), saveMilitaryQualificationInfoDTO.getAuthId())) {
            throw new ServiceException("您已提交过申请，无法重复提交");
        }
        //保存意向用户信息
        OwnerQualification saveOwnerQualification = ConvertUtil.convert(saveMilitaryQualificationInfoDTO, OwnerQualification.class);
        saveOwnerQualification.setIsMilitaryOfficer(1);
        saveOwnerQualification.setProperty(1);
        saveOwnerQualification.setReconsiderationImg(saveMilitaryQualificationInfoDTO.getCreatedUserOrgName());
        this.saveOwnerQualificationInfo(saveOwnerQualification, saveMilitaryQualificationInfoDTO.getCreatedUserId(), saveMilitaryQualificationInfoDTO.getCreatedUserName());

        //清除历史上传的扫描件
        fileService.clearFile(FileTypeEnum.MILITARY_OFFICER_COPY, saveOwnerQualification.getId(), saveMilitaryQualificationInfoDTO.getCreatedUserId(), saveMilitaryQualificationInfoDTO.getCreatedUserName());
        // 保存军官证件照扫描件信息
        fileService.saveFileInfo(FileTypeEnum.MILITARY_OFFICER_COPY, saveMilitaryQualificationInfoDTO.getFile(), saveOwnerQualification.getId()
                , saveMilitaryQualificationInfoDTO.getCreatedUserId(), saveMilitaryQualificationInfoDTO.getCreatedUserName());

        //保存提交并受理办件
        DriverLicenseDTO driverLicense = DriverLicenseDTO.builder()
                .driverLicenseCode(saveMilitaryQualificationInfoDTO.getDriverLicenseCode())
                .driverLicenseIssuingOrganization(saveMilitaryQualificationInfoDTO.getDriverLicenseIssuingOrganization())
                .driverLicenseIssuingPlace(saveMilitaryQualificationInfoDTO.getDriverLicenseIssuingPlace())
                .build();
        //自动分配核查信息
        autoAssignQualificationReview(saveOwnerQualification, driverLicense);
        return new BaseResponse();
    }

    @Override
    public BaseResponse invalidOwnerQualification(InvalidOwnerQualificationDTO invalidOwnerQualificationDTO) {
        Optional<OwnerQualification> optionalOwnerQualification = ownerQualificationMapper.selectByPrimaryKey(invalidOwnerQualificationDTO.getId());
        if (!optionalOwnerQualification.isPresent()) {
            throw new ServiceException("未查询到相应用户信息");
        }
        if (optionalOwnerQualification.get().getExpireTime() == null) {
            throw new ServiceException("该数据无法失效");
        }
        Date now = new Date();
        if (optionalOwnerQualification.get().getExpireTime().compareTo(now) <= 0) {
            throw new ServiceException("该资格信息已过期，无需失效");
        }

        //更新用户购车资格有效期，提前结束
        UpdateStatementProvider updateStatement = SqlBuilder.update(ownerQualification)
                .set(ownerQualification.expireTime).equalTo(now)
                .set(ownerQualification.updatedUserId).equalTo(invalidOwnerQualificationDTO.getOperatorId())
                .set(ownerQualification.updatedUserName).equalTo(invalidOwnerQualificationDTO.getOperatorName())
                .set(ownerQualification.updatedTime).equalTo(now)
                .where(ownerQualification.id, isEqualTo(invalidOwnerQualificationDTO.getId()))
                .and(ownerQualification.expireTime, isNotNull())
                .and(ownerQualification.expireTime, isGreaterThan(now))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        ownerQualificationMapper.update(updateStatement);

        //保存操作日志
        SaveOperateLogDTO saveOperateLogDTO = SaveOperateLogDTO.builder()
                .userOperateTypeEnum(UserOperateTypeEnum.INVALID_OWNER_QUALIFICATION)
                .reason(invalidOwnerQualificationDTO.getReason())
                .ownerQualificationId(invalidOwnerQualificationDTO.getId())
                .operateUserId(invalidOwnerQualificationDTO.getOperatorId())
                .operateUserName(invalidOwnerQualificationDTO.getOperatorName())
                .build();
        userOperateLogService.saveOperateLog(saveOperateLogDTO);
        return new BaseResponse();
    }

    @Override
    public BaseResponse batchInvalidOwnerQualification(BatchInvalidOwnerQualificationDTO batchInvalidOwnerQualificationDTO) {
        final List<OwnerQualification> ownerQualifications = queryOwnerQualifications(batchInvalidOwnerQualificationDTO.getIds());
        for(OwnerQualification qualification : ownerQualifications){
            if (qualification.getExpireTime() == null) {
                continue;
            }
            Date now = new Date();
            if (qualification.getExpireTime().compareTo(now) <= 0) {
                continue;
            }
            //更新用户购车资格有效期，提前结束
            UpdateStatementProvider updateStatement = SqlBuilder.update(ownerQualification)
                    .set(ownerQualification.expireTime).equalTo(now)
                    .set(ownerQualification.updatedUserId).equalTo(batchInvalidOwnerQualificationDTO.getOperatorId())
                    .set(ownerQualification.updatedUserName).equalTo(batchInvalidOwnerQualificationDTO.getOperatorName())
                    .set(ownerQualification.updatedTime).equalTo(now)
                    .where(ownerQualification.id, isEqualTo(qualification.getId()))
                    .and(ownerQualification.expireTime, isNotNull())
                    .and(ownerQualification.expireTime, isGreaterThan(now))
                    .build()
                    .render(RenderingStrategies.MYBATIS3);
            ownerQualificationMapper.update(updateStatement);

            //保存操作日志
            SaveOperateLogDTO saveOperateLogDTO = SaveOperateLogDTO.builder()
                    .userOperateTypeEnum(UserOperateTypeEnum.INVALID_OWNER_QUALIFICATION)
                    .reason(batchInvalidOwnerQualificationDTO.getReason())
                    .ownerQualificationId(qualification.getId())
                    .operateUserId(batchInvalidOwnerQualificationDTO.getOperatorId())
                    .operateUserName(batchInvalidOwnerQualificationDTO.getOperatorName())
                    .build();
            userOperateLogService.saveOperateLog(saveOperateLogDTO);
        }
        return new BaseResponse();
    }

    /**
     * 判断当前用户是否资格核查通过（有效期内）
     *
     * @param authTypeValue 证件类型
     * @param authIdValue   证件号码
     * @return OwnerQualification
     */
    @Override
    public OwnerQualification queryApprovedQualification(Integer authTypeValue, String authIdValue) {
        //查询核查通过且未过期的意向用户记录
        SelectStatementProvider selectStatement = SqlBuilder.select(ownerQualification.allColumns())
                .from(ownerQualification)
                .where()
                .and(ownerQualification.authType, isEqualTo(authTypeValue))
                .and(ownerQualification.authId, isEqualTo(authIdValue))
                .and(ownerQualification.applyStatus, isEqualTo(OwnerQualificationStatusEnum.APPROVE.getStatus()))
                .and(ownerQualification.expireTime, isGreaterThan(new Date()))
                .and(ownerQualification.qualificationType, isEqualTo(OwnerQualificationTypeEnum.NORMAL.getType()))
                .and(ownerQualification.status,isEqualTo(1))
                .limit(1)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        final Optional<OwnerQualification> optionalOwnerQualification = ownerQualificationMapper.selectOne(selectStatement);
        return optionalOwnerQualification.orElse(null);
    }

    @Override
    public OwnerQualification queryLastApprovedQualification(Integer authTypeValue, String authIdValue) {
        //查询最近一条核查通过意向用户记录
        SelectStatementProvider selectStatement = SqlBuilder.select(ownerQualification.allColumns())
                .from(ownerQualification)
                .where()
                .and(ownerQualification.authType, isEqualToWhenPresent(authTypeValue))
                .and(ownerQualification.authId, isEqualTo(authIdValue))
                .and(ownerQualification.applyStatus, isEqualTo(OwnerQualificationStatusEnum.APPROVE.getStatus()))
                .and(ownerQualification.qualificationType, isEqualTo(OwnerQualificationTypeEnum.NORMAL.getType()))
                .and(ownerQualification.status,isEqualTo(1))
                .orderBy(ownerQualification.expireTime.descending())
                .limit(1)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        final Optional<OwnerQualification> optionalOwnerQualification = ownerQualificationMapper.selectOne(selectStatement);
        return optionalOwnerQualification.orElse(null);
    }

    @Override
    public OwnerIdentityInfo queryApprovedQualificationHistory(String authKind, String authId) {
        SelectStatementProvider selectStatement = SqlBuilder.select(ownerIdentityInfo.allColumns())
                .from(ownerIdentityInfo)
                .where(ownerIdentityInfo.authIdentityId, isEqualTo(authId))
                .and(ownerIdentityInfo.authIdentityKind, isEqualTo(authKind))
                .and(ownerIdentityInfo.status, isEqualTo(3))
                .and(ownerIdentityInfo.expireTime, isGreaterThanOrEqualTo(new Date()))
                .limit(1)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        final Optional<OwnerIdentityInfo> ownerIdentityInfo = ownerIdentityInfoMapper.selectOne(selectStatement);
        return ownerIdentityInfo.orElse(null);
    }

    @Override
    public BaseResponse dealOmitQualificationApply() {
        List<String> itemCodes = new ArrayList<String>() {{
            add(LibraryApplyItemEnum.OWNER_QUALIFICATION_APPLY.getItemCode());
        }};
        QueryItemsApplyListResponse queryItemsApplyListResponse = libraryService.queryItemsApplyList(itemCodes, "待预审", 100);
        if (queryItemsApplyListResponse == null || queryItemsApplyListResponse.getData() == null || queryItemsApplyListResponse.getData().getApply() == null) {
            return new BaseResponse("无待预审办件", ResultCode.SUCCESS);
        }
        List<ItemApplyListDTO> itemApplyListDTOList = queryItemsApplyListResponse.getData().getApply();
        for (ItemApplyListDTO itemApply : itemApplyListDTOList) {
            //判断是否是来自一网通办的申请
            Map<String, Object> info = itemApply.getInfo();
            if (info == null) {
                continue;
            }
            String origin = (String) info.get("origin");
            if (StringUtils.compare("extracme", origin) == 0) {
                //TODO 处理来自二手车或管理平台的数据，注:理论上不应该存在需要受理的数据
                //由于二手车小程序的数据直接先存到数据，故可以不通过办件信息获取相关数据
                //根据办件编号查询相关信息
                OwnerQualification relationOwnerQualification = queryOwnerQualificationByApplyNo(itemApply.getApplyNo());
                if (relationOwnerQualification != null){
                    libraryService.acceptApply(itemApply.getApplyNo());
                    if(OwnerQualificationStatusEnum.APPROVE.getStatus().equals(relationOwnerQualification.getApplyStatus())){
                        libraryService.approveApply(itemApply.getApplyNo());
                    }else if(OwnerQualificationStatusEnum.DENY.getStatus().equals(relationOwnerQualification.getApplyStatus())){
                        libraryService.denyApply(itemApply.getApplyNo(), relationOwnerQualification.getReason());
                    }
                }else{
                    libraryService.refuseAcceptApply(itemApply.getApplyNo(),"您提交的信息存在异常，请确保填写信息正确后重新提交");
                }
            } else {
                //处理来自一网通办的数据
                try {
                    OwnerQualificationLibraryInfoDTO libraryInfo = JSON.parseObject(JSON.toJSONString(info), OwnerQualificationLibraryInfoDTO.class);

                    //查询申请经办人信息
                    final GetApplyBaseResponse applyBase = libraryService.getApplyBase(itemApply.getApplyNo());
                    if(applyBase != null && applyBase.getData() != null && !applyBase.getData().isEmpty()){
                        libraryInfo.setLicenseNo(applyBase.getData().get(0).getLicenseNo());
                        libraryInfo.setLicenseType(applyBase.getData().get(0).getLicenseType());
                    }else{
                        log.warn("未查询到办件基础信息:" + itemApply.getApplyNo());
                    }

                    logger.info("处理办件" + itemApply.getApplyNo() + ":" + JSON.toJSONString(libraryInfo));
                    //加锁
                    if(MemcachedUtil.add(itemApply.getApplyNo(),"value", 30)){
                        //处理用户购车资格申请
                        acceptQualificationApply(itemApply.getApplyNo(), itemApply.getParentApplyNo(), libraryInfo);
                    }else{
                        logger.error("已有线程正在处理办件:" + itemApply.getApplyNo());
                    }
                } catch (Exception e) {
                    logger.error(e.getMessage(), e);
                } finally {
                    MemcachedUtil.delete(itemApply.getApplyNo());
                }
            }
        }
        return new BaseResponse();
    }

    @Override
    public void dealQualificationApply(String applyNo, String uapplyNo) {
        GetApplyBaseResponse applyBaseResponse = null;
        for (int i = 0; i < 10; i++) {
            try {
                applyBaseResponse = libraryService.getApplyBase(applyNo);
                break;
            } catch (Exception e) {
                try {
                    Thread.sleep(5000);
                } catch (InterruptedException ex) {
                    ex.printStackTrace();
                }
                continue;
            }
        }

        if (applyBaseResponse == null || applyBaseResponse.getData() == null || applyBaseResponse.getData().size() == 0) {
            return;
        }
        final GetApplyBaseDTO applyBaseInfo = applyBaseResponse.getData().get(0);
        if (StringUtils.compare(applyBaseInfo.getStatus(), "待预审") == 0) {
            OwnerQualificationLibraryInfoDTO libraryInfo = new OwnerQualificationLibraryInfoDTO();
            try {
                //将办件库的Map数据转化成指定类进行处理
                BeanUtils.populate(libraryInfo, applyBaseInfo.getInfo());
                //申请经办人证件编号
                libraryInfo.setLicenseNo(applyBaseInfo.getLicenseNo());
                //申请经办人证件类型
                libraryInfo.setLicenseType(applyBaseInfo.getLicenseType());

                acceptQualificationApply(applyBaseInfo.getApplyNo(), uapplyNo, libraryInfo);
            } catch (Exception e) {
                //终止办件
                logger.error(e.getMessage(),e);
            }
        }
    }

    @Override
    public void dealSupplementQualificationApply(String applyNo) {
        //TODO 如何处理意向的数据

        //查询相关意向用户信息
        final OwnerQualification ownerQualification = queryOwnerQualificationByApplyNo(applyNo);
        if (ownerQualification == null) {
            throw new ServiceException("未查询到相关用户资格信息");
        }
        if (!OwnerQualificationStatusEnum.DENY.getStatus().equals(ownerQualification.getApplyStatus())) {
            throw new ServiceException("当前状态不能进行该操作");
        }
        //获取补正材料基本信息
        final GetApplyStuffResponse applyStuffResponse = libraryService.getApplyStuff(applyNo);
        if (applyStuffResponse == null || applyStuffResponse.getData() == null || applyStuffResponse.getData().size() == 0) {
            //TODO 是否应该拒绝该办件？？
            return;
        }

        //清空之前上传的图片
        fileService.clearFile(FileTypeEnum.QUALIFICATION_PROOF_COPY, ownerQualification.getId(), "-1", "system");

        List<GetApplyStuffDTO> applyStuffList = applyStuffResponse.getData();
        for (GetApplyStuffDTO applyStuff : applyStuffList) {
            if (StringUtils.isNotBlank(applyStuff.getStuffId())) {
                //下载文件
                final ByteArrayResource byteArrayResource = libraryService.downloadStuff(applyStuff.getStuffId());
                String fileExtension = StringUtils.substringAfterLast(applyStuff.getFilename(), ".");

                //保存文件
                fileService.saveFileInfo(FileTypeEnum.QUALIFICATION_PROOF_COPY, fileExtension, byteArrayResource,
                        ownerQualification.getId(), Const.ADMIN_ORG_ID, Const.ADMIN_ID, Const.ADMIN_NAME);
            }
        }
        //将办件状态更新为复核
        reconsiderQualification(ownerQualification.getId(), Const.ADMIN_ID, Const.ADMIN_NAME, "一网通办");

        //受理办件
        libraryService.acceptApply(applyNo);
        //更新指南
        libraryService.changeStatus(applyNo, AnnouncementEnum.QUALIFICATION_ACCEPTED);
    }

    @Override
    public BaseResponse reconsiderOwnerQualification(ReconsiderOwnerQualificationDTO reconsiderOwnerQualificationDTO) {
        Optional<OwnerQualification> optionalOwnerQualification = ownerQualificationMapper.selectByPrimaryKey(reconsiderOwnerQualificationDTO.getOwnerQualificationId());
        if (!optionalOwnerQualification.isPresent()) {
            throw new ServiceException("未查询到相应购车资格信息");
        }
        if (!OwnerQualificationStatusEnum.DENY.getStatus().equals(optionalOwnerQualification.get().getApplyStatus())) {
            throw new ServiceException("当前状态不能进行该操作");
        }

        //  可以简化成针对状态+有效期的判断
        //复核条件判断
        if (!"test".equals(SpringContextUtil.getActiveProfile()) && !"dev".equals(SpringContextUtil.getActiveProfile())) {
            if (!canQualificationReconsider(optionalOwnerQualification.get())) {
                throw new ServiceException("当前状态不能进行复核");
            }
        }


        // 判断是否有照片，是否之前复核过，只能复核一次
        List<FileInfoDTO> fileInfo = fileService.getFileInfo(FileTypeEnum.QUALIFICATION_PROOF_COPY, optionalOwnerQualification.get().getId());
        if (!fileInfo.isEmpty()) {
            throw new ServiceException("该记录已复核过，不可多次复核，请重新创建并提交申请");
        }

        //清空之前上传的图片
        fileService.clearFile(FileTypeEnum.QUALIFICATION_PROOF_COPY, optionalOwnerQualification.get().getId(), reconsiderOwnerQualificationDTO.getUpdatedUserId(), reconsiderOwnerQualificationDTO.getUpdatedUserName());
        //保存上传的文件
        fileService.saveFileInfo(FileTypeEnum.QUALIFICATION_PROOF_COPY, reconsiderOwnerQualificationDTO.getFiles(), reconsiderOwnerQualificationDTO.getOwnerQualificationId(),
                reconsiderOwnerQualificationDTO.getUpdatedUserId(), reconsiderOwnerQualificationDTO.getUpdatedUserName());

        //将办件状态更新为复核
        reconsiderQualification(optionalOwnerQualification.get().getId(), Const.ADMIN_ID, Const.ADMIN_NAME,"二手车平台");
        if (StringUtils.isNotBlank(optionalOwnerQualification.get().getApplyNo())) {
            //受理办件
            libraryService.acceptApply(optionalOwnerQualification.get().getApplyNo());
            //更新指南
            libraryService.changeStatus(optionalOwnerQualification.get().getApplyNo(), AnnouncementEnum.QUALIFICATION_ACCEPTED);
        }
        return new BaseResponse();
    }

    @Override
    public BaseResponse dealExpireReconsiderQualification() {
        //查询被拒绝且已经过期的用户资格
        SelectStatementProvider selectStatement = SqlBuilder.select(ownerQualification.allColumns())
                .from(ownerQualification)
                .where(ownerQualification.applyStatus, isEqualTo(OwnerQualificationStatusEnum.DENY.getStatus()))
                .and(ownerQualification.expireTime, isLessThanOrEqualTo(new Date()))
                .and(ownerQualification.qualificationType, isEqualTo(OwnerQualificationTypeEnum.NORMAL.getType()))
                .and(ownerQualification.status,isEqualTo(1))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        final List<OwnerQualification> ownerQualificationList = ownerQualificationMapper.selectMany(selectStatement);
        for (OwnerQualification expiredOwnerQualification : ownerQualificationList) {
            //如果有办件编号
            if (StringUtils.isNotBlank(expiredOwnerQualification.getApplyNo())) {
                //拒绝办件
                libraryService.acceptApply(expiredOwnerQualification.getApplyNo());

                libraryService.denyApply(expiredOwnerQualification.getApplyNo(), "未在规定时间内上传复核材料");

                libraryService.changeStatus(expiredOwnerQualification.getApplyNo(), AnnouncementEnum.QUALIFICATION_DENY);
            }

            //更新用户资格信息
            UpdateStatementProvider updateStatement = SqlBuilder.update(ownerQualification)
                    .set(ownerQualification.updatedUserId).equalTo(Const.ADMIN_ID)
                    .set(ownerQualification.updatedUserName).equalTo(Const.SCHEDULE)
                    .set(ownerQualification.expireTime).equalToNull()
                    .set(ownerQualification.updatedTime).equalTo(new Date())
                    .where(ownerQualification.id, isEqualTo(expiredOwnerQualification.getId()))
                    .build()
                    .render(RenderingStrategies.MYBATIS3);
            ownerQualificationMapper.update(updateStatement);
        }
        return new BaseResponse();
    }

    @Override
    public AbnormalQualificationResponse searchAbnormalQualification(SearchAbnormalQualificationDTO searchAbnormalQualificationDTO) {

        AbnormalQualificationResponse result = new AbnormalQualificationResponse();
        //查询信用异常记录
        //考虑到信用记录理论上一般会及时查询反馈，且之后会每间隔2小时重复查询，所以未出结果信用数据都为异常数据
        SelectStatementProvider selectStatement = SqlBuilder.select(
                ownerQualificationReviewDetail.id,
                ownerQualification.id,
                ownerQualification.name,
                ownerQualification.authId,
                ownerQualification.createdTime,
                ownerQualificationReviewDetail.remark)
                .from(ownerQualificationReviewDetail)
                .leftJoin(ownerQualification)
                .on(ownerQualification.id, equalTo(ownerQualificationReviewDetail.ownerQulificationId))
                .where(ownerQualificationReviewDetail.reviewStatus, isEqualTo(0))
                .and(ownerQualificationReviewDetail.reviewType, isEqualTo(ReviewTypeEnum.CREDIT.getValue()))
                .limit(searchAbnormalQualificationDTO.getPageSize())
                .offset((searchAbnormalQualificationDTO.getPageNum() - 1) * searchAbnormalQualificationDTO.getPageSize())
                .build()
                .render(RenderingStrategies.MYBATIS3);

        final List<AbnormalCreditQualificationDTO> abnormalCreditQualification = ownerQualificationMapper.searchAbnormalCreidtQualification(selectStatement);

        SelectStatementProvider countStatement = SqlBuilder.select(count())
                .from(ownerQualificationReviewDetail)
                .where(ownerQualificationReviewDetail.reviewStatus, isEqualTo(0))
                .and(ownerQualificationReviewDetail.reviewType, isEqualTo(ReviewTypeEnum.CREDIT.getValue()))
                .build()
                .render(RenderingStrategies.MYBATIS3);

        long count = ownerQualificationMapper.count(countStatement);
        result.setAbnormalCreditQualification(new PageInfoBO<>(count,abnormalCreditQualification));

        //TODO 查询社保异常记录（目前木有）

        //TODO 查询公安异常记录
        return result;
    }


    @Override
    public OwnerSupplementInfoDTO queryOwnerSupplementInfo(String applyNo) {
        OwnerQualification qualification = queryOwnerQualificationByApplyNo(applyNo);
        if (qualification == null) {
            throw new ServiceException("未查询到相关办件信息");
        }
        //用户资格信息
        OwnerQualificationInfoDTO qualificationInfo = new OwnerQualificationInfoDTO();
        qualificationInfo.setName(qualification.getName());
        qualificationInfo.setAuthType(qualification.getAuthType());
        qualificationInfo.setAuthId(qualification.getAuthId());
        qualificationInfo.setProperty(qualification.getProperty());
        qualificationInfo.setQualificationReviewStatus(qualification.getApplyStatus());
        qualificationInfo.setQualificationReviewReason(qualification.getReason());
        qualificationInfo.setQualificationSubmitTime(qualification.getApplyTime());

        //用户公安驾照信息
        OwnerTrafficQualification trafficQualification = ownerTrafficQualificationService.queryOwnerTrafficQualification(qualification.getId());
        if (trafficQualification != null) {
            qualificationInfo.setDriverLicenseCode(trafficQualification.getDriverLicenseCode());
            qualificationInfo.setDriverLicenseIssuingOrganization(trafficQualification.getDriverLicenseIssuingOrganization());
            qualificationInfo.setDriverLicenseIssuingPlace(trafficQualification.getDriverLicenseIssuingPlace());
        }

        OwnerSupplementInfoDTO ownerSupplementInfo = new OwnerSupplementInfoDTO();
        ownerSupplementInfo.setOwnerQualificationInfo(qualificationInfo);
        return ownerSupplementInfo;
    }

    @Override
    public BaseResponse dealPendingQualificationReview() {
        Set<Long> pendQualificationIdSet = new HashSet<>();

        //查询未核查完成的社保
        List<OwnerQualificationReviewDetail> socialReviewList = socialService.queryPendingReview();
        final List<Long> socialQualificationIds = socialReviewList.stream().map(OwnerQualificationReviewDetail::getOwnerQulificationId).collect(Collectors.toList());
        pendQualificationIdSet.addAll(socialQualificationIds);
        List<OwnerQualification> socialQualificationList = queryOwnerQualifications(socialQualificationIds);
        for (OwnerQualification socialQualification : socialQualificationList) {
            //重新核查社保信息
            newSocialService.review(socialQualification.getId(), socialQualification.getName(), socialQualification.getAuthType(), socialQualification.getAuthId());
        }

        //查询信用
        List<OwnerQualificationReviewDetail> creditReviewList = creditService.queryPendingReview();
        final List<Long> creditQualificationIds = creditReviewList.stream().map(OwnerQualificationReviewDetail::getOwnerQulificationId).collect(Collectors.toList());
        pendQualificationIdSet.addAll(creditQualificationIds);
        List<OwnerQualification> creditQualificationList = queryOwnerQualifications(creditQualificationIds);
        for (OwnerQualification creditQualification : creditQualificationList) {
            //重新核查社保信息
            creditService.review(creditQualification.getId(), creditQualification.getProperty(), creditQualification.getName(), creditQualification.getAuthId(), true);
        }

        //查询本地公安
        List<OwnerQualificationReviewDetail> trafficReviewList = driverService.queryPendingReview();
        final List<Long> trafficQualificationIds = trafficReviewList.stream().map(OwnerQualificationReviewDetail::getOwnerQulificationId).collect(Collectors.toList());
        pendQualificationIdSet.addAll(trafficQualificationIds);
        List<OwnerQualification> trafficQualificationList = queryOwnerQualifications(trafficQualificationIds);
        for (OwnerQualification trafficQualification : trafficQualificationList) {
            //重新核查公安信息
            driverService.review(trafficQualification.getId());
        }

        // 查询居住证
        List<OwnerQualificationReviewDetail> residencePermitReviewList = residencePermitService.queryPendingReview();
        final List<Long> residencePermitIds = residencePermitReviewList.stream().map(OwnerQualificationReviewDetail::getOwnerQulificationId).collect(Collectors.toList());
        pendQualificationIdSet.addAll(residencePermitIds);
        List<OwnerQualification> residencePermitQualificationList = queryOwnerQualifications(residencePermitIds);
        for (OwnerQualification residencePermitQualification : residencePermitQualificationList) {
            //重新核查居住证信息
            residencePermitService.review(residencePermitQualification.getId(), residencePermitQualification.getName(), residencePermitQualification.getAuthId(), true);
        }

        // 查询交委
        List<OwnerQualificationReviewDetail> trafficCommitteeReviewList = trafficCommitteeService.queryPendingLicenseLimitReview();
        final List<Long> trafficCommitteeIds = trafficCommitteeReviewList.stream().map(OwnerQualificationReviewDetail::getOwnerQulificationId).collect(Collectors.toList());
        pendQualificationIdSet.addAll(trafficCommitteeIds);
        List<OwnerQualification> trafficCommitteeQualificationList = queryOwnerQualifications(trafficCommitteeIds);
        for (OwnerQualification trafficCommitteeQualification : trafficCommitteeQualificationList) {
            //重新核查交委信息
            trafficCommitteeService.reviewLicenseLimit(trafficCommitteeQualification.getId(), true);
        }

        //查询特殊人才
        List<OwnerQualificationReviewDetail> specialTrafficReviewList = driverService.querySpecialPendingReview();
        final List<Long> specialTrafficQualificationIds = specialTrafficReviewList.stream().map(OwnerQualificationReviewDetail::getOwnerQulificationId).distinct().collect(Collectors.toList());
        List<OwnerQualification> specialTrafficQualificationList = queryOwnerQualifications(specialTrafficQualificationIds);
        for (OwnerQualification specialTrafficQualification : specialTrafficQualificationList) {
            driverService.review(specialTrafficQualification.getId(), true);
        }

        //查询交通委以旧换新额度
        List<OwnerQualificationReviewDetail> exchangeLimitReviewList = trafficCommitteeService.queryPendingExchangeLimitReview();
        final List<Long> exchangeLimitQualificationIds = exchangeLimitReviewList.stream().map(OwnerQualificationReviewDetail::getOwnerQulificationId).collect(Collectors.toList());
        for (Long exchangeLimitQualificationId : exchangeLimitQualificationIds) {
            trafficCommitteeService.reviewExchangeLimit(exchangeLimitQualificationId);
        }
        return new BaseResponse();
    }

    @Override
    public boolean hasReview(Long ownerQualificationId, Integer reviewType) {
        SelectStatementProvider render = countFrom(ownerQualificationReviewDetail)
                .where(ownerQualificationReviewDetail.ownerQulificationId, isEqualTo(ownerQualificationId))
                .and(ownerQualificationReviewDetail.status, isEqualTo(1))
                .and(ownerQualificationReviewDetail.reviewType, isEqualTo(reviewType))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        long count = ownerQualificationReviewDetailMapper.count(render);
        if (count > 0) {
            return true;
        }
        return false;
    }

    @Override
    public CurrentOwnerQualificationDTO queryCurrentOwnerQualification(Integer authType, String authId) {
        CurrentOwnerQualificationDTO currentOwnerQualification;
        //查询最近一条意向用户记录
        SelectStatementProvider selectStatement = select(ownerQualification.allColumns())
                .from(ownerQualification)
                .where()
                .and(ownerQualification.authId, isEqualTo(authId))
                .and(ownerQualification.authType, isEqualTo(authType))
                .and(ownerQualification.qualificationType, isEqualTo(OwnerQualificationTypeEnum.NORMAL.getType()))
                .and(ownerQualification.status,isEqualTo(1))
                .orderBy(ownerQualification.id.descending())
                .limit(1)
                .build().render(RenderingStrategies.MYBATIS3);
        Optional<OwnerQualification> optionalOwnerQualification = ownerQualificationMapper.selectOne(selectStatement);
        if(optionalOwnerQualification.isPresent()){
            currentOwnerQualification = ConvertUtil.convert(optionalOwnerQualification.get(), CurrentOwnerQualificationDTO.class);
        }else{
            currentOwnerQualification = new CurrentOwnerQualificationDTO();
        }
        return currentOwnerQualification;
    }

    @Override
    public void cancelReconsiderOwnerQualification(Long id, String reason, String operatorId, String operatorName){
        Optional<OwnerQualification> optionalOwnerQualification = ownerQualificationMapper.selectByPrimaryKey(id);
        if (!optionalOwnerQualification.isPresent()) {
            throw new ServiceException("未查询到相应购车资格信息");
        }
        if(!OwnerQualificationStatusEnum.DENY.getStatus().equals(optionalOwnerQualification.get().getApplyStatus())){
            throw new ServiceException("当前购车资格状态无法取消复核");
        }
        if (optionalOwnerQualification.get().getExpireTime() == null) {
            throw new ServiceException("该购车资格信息已取消复核");
        }
        Date now = new Date();
        if (optionalOwnerQualification.get().getExpireTime().compareTo(now) <= 0) {
            throw new ServiceException("该购车资格信息已过期，无需失效");
        }

        //更新用户购车资格有效期，提前结束
        UpdateStatementProvider updateStatement = SqlBuilder.update(ownerQualification)
                .set(ownerQualification.expireTime).equalTo(now)
                .set(ownerQualification.updatedUserId).equalTo(operatorId)
                .set(ownerQualification.updatedUserName).equalTo(operatorName)
                .set(ownerQualification.updatedTime).equalTo(now)
                .where(ownerQualification.id, isEqualTo(id))
                .and(ownerQualification.expireTime, isNotNull())
                .and(ownerQualification.expireTime, isGreaterThan(now))
                .and(ownerQualification.applyStatus, isEqualTo(OwnerQualificationStatusEnum.DENY.getStatus()))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        ownerQualificationMapper.update(updateStatement);

        //保存操作日志
        SaveOperateLogDTO saveOperateLogDTO = SaveOperateLogDTO.builder()
                .userOperateTypeEnum(UserOperateTypeEnum.CANCEL_RECONSIDER_OWNER_QUALIFICATION)
                .reason(reason)
                .ownerQualificationId(id)
                .operateUserId(operatorId)
                .operateUserName(operatorName)
                .build();
        userOperateLogService.saveOperateLog(saveOperateLogDTO);
    }

    @Override
    public QueryOwnerBaseQualificationResponse queryOwnerBaseQualification(String name, Integer authType, String authId) {
        QueryOwnerBaseQualificationResponse response = new QueryOwnerBaseQualificationResponse();
        Boolean residenceResult = residencePermitService.hasResidencePermit(name, authId);
        response.setResidenceResult(residenceResult);
        return response;
    }


    @Override
    public void queryOuterDriverHasNewEnergy() {
        // 外地需要核查的记录 驾照号为身份证号（来源owner_qualification）
        List<OwnerTrafficQualification> trafficQualifications = driverService.queryOuterDriverPendingReview();
        if (trafficQualifications.size() != 0) {
            trafficQualifications.parallelStream().forEach(data -> {
                try {
                    OwnerQualification qualification = getOwnerQualificationById(data.getOwnerQualificationId());

                    if (Integer.valueOf(1).equals(data.getHasDriverLicense())) {
                        Boolean hasNewEnergyVehicle = driverService.hasNewEnergyVehicle(data.getDriverLicenseCode());
                        data.setHasNewEnergyVehicle(tranHasNewEnergyVehicle(hasNewEnergyVehicle));
                    }
                    data.setReviewStatus(trafficAutoApprove(qualification,  data));
                }catch (Exception e) {
                    // 不做任何处理
                    log.error(e.getMessage(), e);
                }
            });
        }
        for (OwnerTrafficQualification item : trafficQualifications) {
            if (item.getReviewStatus() != null && !Integer.valueOf(0).equals(item.getReviewStatus())) {
                // 只有已经核查了的数据， 才会进行更新
                UpdateStatementProvider updateTraffic = update(ownerTrafficQualification)
                        .set(ownerTrafficQualification.hasNewEnergyVehicle).equalTo(item.getHasNewEnergyVehicle())
                        .set(ownerTrafficQualification.reviewStatus).equalTo(item.getReviewStatus())
                        .set(ownerTrafficQualification.reason).equalTo(item.getReason())
                        .where(ownerTrafficQualification.id, isEqualTo(item.getId()))
                        .and(ownerTrafficQualification.reviewStatus, isEqualTo(0))
                        .build().render(RenderingStrategies.MYBATIS3);
                ownerQualificationReviewDetailMapper.update(updateTraffic);
                //更新核查记录明细（用于之后自动核查）
                UpdateStatementProvider updateReviewDetailStatement = update(ownerQualificationReviewDetail)
                        .set(ownerQualificationReviewDetail.reviewStatus).equalTo(item.getReviewStatus())
                        .set(ownerQualificationReviewDetail.reason).equalTo(item.getReason())
                        .set(ownerQualificationReviewDetail.updatedUserId).equalTo(Const.ADMIN_ID)
                        .set(ownerQualificationReviewDetail.updatedUserName).equalTo(Const.ADMIN_NAME)
                        .set(ownerQualificationReviewDetail.updatedTime).equalTo(new Date())
                        .where(ownerQualificationReviewDetail.reviewType, isEqualTo(0))
                        .and(ownerTrafficQualification.reviewStatus, isEqualTo(0))
                        .and(ownerQualificationReviewDetail.ownerQulificationId, isEqualTo(item.getOwnerQualificationId()))
                        .build()
                        .render(RenderingStrategies.MYBATIS3);
                ownerQualificationReviewDetailMapper.update(updateReviewDetailStatement);
            }else {
                log.warn("本次公安核查 ownerQualificationId:"+ item.getOwnerQualificationId() + ", 未进行核查");
            }
        }
    }

    /**
     * 办件办件编号查询相关联意向用户信息
     * @param applyNo 统一审批编码
     * @return OwnerQualification
     */
    private OwnerQualification queryOwnerQualificationByApplyNo(String applyNo) {
        if (StringUtils.isBlank(applyNo)) {
            return null;
        }
        SelectStatementProvider selectStatement = SqlBuilder.select(ownerQualification.allColumns())
                .from(ownerQualification)
                .where(ownerQualification.applyNo, isEqualTo(applyNo))
                .and(ownerQualification.qualificationType, isEqualTo(OwnerQualificationTypeEnum.NORMAL.getType()))
                .and(ownerQualification.status,isEqualTo(1))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        final Optional<OwnerQualification> result = ownerQualificationMapper.selectOne(selectStatement);
        return result.orElse(null);
    }

    /**
     * 受理用户资格相关申请的办件
     *
     * @param applyNo     统一审批编码
     * @param uapplyNo    联办审批编码
     * @param libraryInfo 办件信息
     */
    public void acceptQualificationApply(String applyNo, String uapplyNo, OwnerQualificationLibraryInfoDTO libraryInfo) {
        // 校验一网通办数据（能否受理）
        //查询有无未完成的用户购车资格申请
        Integer authType = Integer.parseInt(libraryInfo.getAuthType().get("value"));
        String authId = libraryInfo.getAuthId();

        if(StringUtils.isBlank(libraryInfo.getProperty().get("value"))){
            libraryService.refuseAcceptApply(applyNo, "您提交的办件信息存在异常，请确保填写信息正确后重新提交");
            return;
        }
        if (authType == null || StringUtils.isBlank(authId)) {
            libraryService.refuseAcceptApply(applyNo, "您提交的证件信息存在异常，请确保填写信息正确后重新提交");
            return;
        }
        if(StringUtils.isNotBlank(libraryInfo.getDriverLicenseIssuingOrganization()) && libraryInfo.getDriverLicenseIssuingOrganization().length() > 30){
            libraryService.refuseAcceptApply(applyNo, "您提交的驾驶证发证机关填写信息异常，请确保填写信息正确后重新提交");
            return;
        }

        //车辆类型校验-混动则直接拒绝
        if (libraryInfo.getVehicleModelType() != null) {
            //如果当前时间大于2023年1月1日，则拒绝接受关于混动车的购车资质申请
            Date validDate = DateUtil.parse("2023-01-01", DateUtil.DATE_TYPE3);
            if(new Date().after(validDate) && Integer.valueOf(2).equals(Integer.valueOf(libraryInfo.getVehicleModelType().get("value")))){
                logger.warn("逾期提交混动购车资质申请:" + applyNo);
                libraryService.refuseAcceptApply(applyNo, "根据《上海市鼓励购买和使用新能源汽车实施办法》（沪府办规〔2021〕3号）相关要求，自2023年1月1日起，不再接受插电式混合动力车辆购车资质申请，请重新提交。");
                return;
            }
        }

        //已存在未完成的用户资格
        final OwnerQualification unfinishedQualification = getUnfinishedQualification(authType, authId);
        if (unfinishedQualification != null) {
            //如果当前办件为未完成的办件
            if(StringUtils.equals(unfinishedQualification.getApplyNo(), applyNo)) {
                Long oneHourAgo = System.currentTimeMillis() - TimeUnit.HOURS.toMillis(1);
                //如果已经超过1个小时
                if(unfinishedQualification.getCreatedTime().getTime() < oneHourAgo){
                    libraryService.acceptApply(applyNo);
                    //更新同步办件的状态为数据库的状态
                    if(OwnerQualificationStatusEnum.APPROVE.getStatus().equals(unfinishedQualification.getApplyStatus())){
                        libraryService.approveApply(applyNo);
                    }else if(OwnerQualificationStatusEnum.DENY.getStatus().equals(unfinishedQualification.getApplyStatus())){
                        libraryService.denyApply(applyNo, unfinishedQualification.getReason());
                    }
                }else{
                    //万达办件状态更新延迟
                }
            }
            //已经存在其它进行中的办件，则不受理办件
            else {
                //根据未完成的办件状态，发送不同的不受理原因
                //当存在未过期的审核通过办件时
                if (OwnerQualificationStatusEnum.APPROVE.getStatus().equals(unfinishedQualification.getApplyStatus())) {
                    String content = "由于您的购车资格查询已经通过，有效期至"+ DateUtil.format(unfinishedQualification.getExpireTime(), DateUtil.DATE_TYPE3) +"，故本次办件["+ applyNo +"]申请不予受理。请勿在有效期内重复查询";
                    libraryService.refuseAcceptApply(applyNo, content);
                }
                //当存在审核拒绝的办件时
                else {
                    String content;
                    //针对审批中的重复提交办件
                    if(unfinishedQualification.getExpireTime() == null){
                        content = "由于您已于" + DateUtil.format(unfinishedQualification.getApplyTime(), DateUtil.DATE_TYPE3) +"提交购车资质查询，目前正在审核中（不含提交当日和法定节假日，第5个工作日24点前出审核结果），故本次办件["+ applyNo +"]申请不予受理，请勿重复提交";
                    }
                    //针对复核中的重复提交办件
                    else{
                        content = "由于您已于" + DateUtil.format(unfinishedQualification.getApplyTime(), DateUtil.DATE_TYPE3) +"提交购车资质查询，目前已进入补正流程，故本次办件["+ applyNo +"]申请不予受理，您可在"+
                                DateUtil.format(unfinishedQualification.getExpireTime(), DateUtil.DATE_TYPE3) +"前联系4S店提交补正材料或终止补正流程，补正流程可咨询021-58812035（工作日上午9:00-11:30，下午1:30-5:00）,期间请勿重复提交";
                    }
                    libraryService.refuseAcceptApply(applyNo, content);
                }
                //设置指南页面
                libraryService.changeStatus(applyNo, AnnouncementEnum.QUALIFICATION_REFUSE_ACCEPT);
            }
            return;
        }



        //保存意向用户
        try {
            OwnerQualification acceptOwnerQualification = new OwnerQualification();
            acceptOwnerQualification.setProperty(Integer.parseInt(libraryInfo.getProperty().get("value")));
            if (StringUtils.isNotBlank(libraryInfo.getName())) {
                acceptOwnerQualification.setName(libraryInfo.getName());
            } else {
                acceptOwnerQualification.setName(libraryInfo.getTargetName());
            }
            acceptOwnerQualification.setAuthId(libraryInfo.getAuthId());
            acceptOwnerQualification.setAuthType(Integer.parseInt(libraryInfo.getAuthType().get("value")));
            acceptOwnerQualification.setIsMilitaryOfficer(0);
            acceptOwnerQualification.setMobilePhone(libraryInfo.getMobilePhone());
            acceptOwnerQualification.setApplyNo(applyNo);
            acceptOwnerQualification.setUapplyNo(uapplyNo);
            acceptOwnerQualification.setUserId(libraryInfo.getUserId());
            //申请经办人信息
            acceptOwnerQualification.setApplyLicenseNo(libraryInfo.getLicenseNo());
            acceptOwnerQualification.setApplyLicenseType(libraryInfo.getLicenseType());

            // 需要保存购车的车辆类型
            if (libraryInfo.getVehicleModelType() != null) {
                acceptOwnerQualification.setVehicleModelType(Integer.valueOf(libraryInfo.getVehicleModelType().get("value")));
            }

            DriverLicenseDTO driverLicense = null;
            //如果是私人购车，则需要查询驾照信息
            if (acceptOwnerQualification.getProperty() == 1) {
                if (StringUtils.isNotBlank(libraryInfo.getHouseholdRegistrationType().get("value"))) {
                    acceptOwnerQualification.setHouseholdRegistrationType(Integer.parseInt(libraryInfo.getHouseholdRegistrationType().get("value")));
                }
                driverLicense = DriverLicenseDTO.builder()
                        .driverLicenseCode(libraryInfo.getDriverLicenseCode())
                        .driverLicenseIssuingPlace(libraryInfo.getDriverLicenseIssuingPlace().get("name"))
                        .driverLicenseIssuingOrganization(libraryInfo.getDriverLicenseIssuingOrganization())
                        .driverFileNo(libraryInfo.getDriverFileNo())
                        .build();
            }
            saveOwnerQualificationInfo(acceptOwnerQualification, "-1", "schedule");

            //保存办件数据（只是存储，无实际意义）
            OnethingApplyInfo saveOnethingApplyInfo = new OnethingApplyInfo();
            saveOnethingApplyInfo.setItemCode(LibraryApplyItemEnum.OWNER_QUALIFICATION_APPLY.getItemCode());
            saveOnethingApplyInfo.setApplyNo(applyNo);
            saveOnethingApplyInfo.setUapplyNo(uapplyNo);
            saveOnethingApplyInfo.setOwnerQualificationId(acceptOwnerQualification.getId());
            saveOnethingApplyInfo.setApplyStatus(ApplyStatusEnum.WAITING_ACCEPT.getStatus());
            saveOnethingApplyInfo.setCreatedTime(new Date());
            saveOnethingApplyInfo.setCreatedUserId(acceptOwnerQualification.getCreatedUserId());
            saveOnethingApplyInfo.setCreatedUserName(acceptOwnerQualification.getCreatedUserName());
            onethingApplyInfoMapper.insertSelective(saveOnethingApplyInfo);

            //受理办件
            libraryService.acceptApply(applyNo);

            //设置指南页面(已经废弃，可不存储，无实际意义）
            libraryService.changeStatus(applyNo, AnnouncementEnum.QUALIFICATION_ACCEPTED);

            //自动分发审批
            autoAssignQualificationReview(acceptOwnerQualification, driverLicense);
        } catch (Exception e) {
            logger.error("办件参数异常:" + e.getMessage() + ",办件编号:" + applyNo + ",联办件编号：" + uapplyNo + "，办件信息" + libraryInfo, e);
//            //不受理办件
//            libraryService.refuseAcceptApply(applyNo, "提交办件数据处理异常，请尝试重新提交。");
            //设置指南页面
            libraryService.changeStatus(applyNo, AnnouncementEnum.QUALIFICATION_REFUSE_ACCEPT);
        }
    }

    /**
     * 自动审批用户资格信息（全量）
     * 针对所有目前购车资格状态为核查中的用户，自动进行核查办件
     */
    @Override
    public BaseResponse autoReviewOwnerQualification() {
        //查询所有核查中的购车资格信息
        SelectStatementProvider selectStatement = SqlBuilder.select(ownerQualification.allColumns())
                .from(ownerQualification)
                .where(ownerQualification.applyStatus, isEqualTo(0))
                .and(ownerQualification.status,isEqualTo(1))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        final List<OwnerQualification> ownerQualificationList = ownerQualificationMapper.selectMany(selectStatement);
        //遍历用户查询，自动审核用户购车资质信息
        for(OwnerQualification needReviewOwnerQualification: ownerQualificationList){
            asyncService.autoReviewOwnerQualification(needReviewOwnerQualification);
        }
        return new BaseResponse();
    }

    /**
     * 自动审批用户资格信息
     *
     * @param ownerQualificationId the owner qualification id
     */
    private void autoReviewOwnerQualification(Long ownerQualificationId) {
        Optional<OwnerQualification> optionalOwnerQualification = ownerQualificationMapper.selectByPrimaryKey(ownerQualificationId);
        if(optionalOwnerQualification.isPresent() && optionalOwnerQualification.get().getApplyStatus() == 0){
            autoReviewOwnerQualification(optionalOwnerQualification.get());
        }
    }

    /**
     * 根据办件信息，生成驾照信息
     * @param property 用户性质
     * @param libraryInfo 办件信息
     * @return
     */
    private DriverLicenseDTO generateDriverLicenseInfo(Integer property, OwnerQualificationLibraryInfoDTO libraryInfo){
        DriverLicenseDTO driverLicense = null;
        if (property == 1) {
            driverLicense = DriverLicenseDTO.builder()
                    .driverLicenseCode(libraryInfo.getDriverLicenseCode())
                    .driverLicenseIssuingPlace(libraryInfo.getDriverLicenseIssuingPlace().get("name"))
                    .driverLicenseIssuingOrganization(libraryInfo.getDriverLicenseIssuingOrganization())
                    .driverFileNo(libraryInfo.getDriverFileNo())
                    .build();
        }
        return driverLicense;
    }

    /**
     * 修复用户购车资质信息
     * @param reviewOwnerQualificationInfo 需修复的购车资质申请信息
     */
    private void recoverOwnerQualificationReviewDetail(OwnerQualification reviewOwnerQualificationInfo){
        //由于小程序的办件库信息不全，无法复原，所以直接拒绝，要求用户重新提交或者不提交
        if(StringUtils.compare(reviewOwnerQualificationInfo.getCreatedUserName(), Const.SMALL_PROGRAM)==0){
            libraryService.refuseAcceptApply(reviewOwnerQualificationInfo.getApplyNo(), "办件处理异常，请重新提交");
            return;
        }

        //重新从办件库中获取信息
        GetApplyBaseResponse applyBaseResponse = libraryService.getApplyBase(reviewOwnerQualificationInfo.getApplyNo());
        if (applyBaseResponse == null || applyBaseResponse.getData() == null || applyBaseResponse.getData().size() == 0) {
            return;
        }
        final GetApplyBaseDTO applyBaseInfo = applyBaseResponse.getData().get(0);
        try {
            OwnerQualificationLibraryInfoDTO libraryInfo = new OwnerQualificationLibraryInfoDTO();
            //将办件库的Map数据转化成指定类进行处理
            BeanUtils.populate(libraryInfo, applyBaseInfo.getInfo());

            //针对私人用户，填充户籍信息
            if (reviewOwnerQualificationInfo.getProperty() == 1 && StringUtils.isNotBlank(libraryInfo.getHouseholdRegistrationType().get("value"))){
                reviewOwnerQualificationInfo.setHouseholdRegistrationType(Integer.parseInt(libraryInfo.getHouseholdRegistrationType().get("value")));
            }
            //组装驾照信息
            DriverLicenseDTO driverLicense = generateDriverLicenseInfo(reviewOwnerQualificationInfo.getProperty(), libraryInfo);


            //判断是否是特殊人才
            boolean isSpecialOwner = ownerQualificationSpecialService.isSpecialOwner(reviewOwnerQualificationInfo.getAuthId());
            if(isSpecialOwner){
                logger.warn("该用户：{}为特殊人才，略过常规校验。", reviewOwnerQualificationInfo.getAuthId());
                //查询驾照信息-仅查询名下是否有新能源车
                if (reviewOwnerQualificationInfo.getProperty() != 2) {
                    driverService.review(reviewOwnerQualificationInfo.getId(), driverLicense, true);
                }
            }

            //正常流程
            else{
                //查询信用信息
                creditService.review(reviewOwnerQualificationInfo.getId(), reviewOwnerQualificationInfo.getProperty(), reviewOwnerQualificationInfo.getName(), reviewOwnerQualificationInfo.getAuthId());

                //查询驾照信息（除法人）
                if (reviewOwnerQualificationInfo.getProperty() != 2) {
                    driverService.review(reviewOwnerQualificationInfo.getId(), driverLicense);
                }

                if (reviewOwnerQualificationInfo.getProperty() == 2 || reviewOwnerQualificationInfo.getHouseholdRegistrationType() != 0) {
                    newSocialService.review(reviewOwnerQualificationInfo.getId(), reviewOwnerQualificationInfo.getName(), reviewOwnerQualificationInfo.getAuthType(), reviewOwnerQualificationInfo.getAuthId());
                }

                //查询居住证（外省市（不包括港澳台））除军官
                if (reviewOwnerQualificationInfo.getProperty() != 2 && reviewOwnerQualificationInfo.getHouseholdRegistrationType() == 1 && reviewOwnerQualificationInfo.getIsMilitaryOfficer() == 0) {
                    residencePermitService.review(reviewOwnerQualificationInfo.getId(),reviewOwnerQualificationInfo.getName(), reviewOwnerQualificationInfo.getAuthId());
                }

                //交通委大牌额度
                if(reviewOwnerQualificationInfo.getProperty() != 2){
                    trafficCommitteeService.reviewLicenseLimit(reviewOwnerQualificationInfo.getId());
                }
            }


            //针对为待预审的办件，进行受理
            if (StringUtils.compare(applyBaseInfo.getStatus(), "待预审") == 0 || StringUtils.compare(applyBaseInfo.getStatus(), "不予受理") == 0) {
                //受理办件
                libraryService.acceptApply(reviewOwnerQualificationInfo.getApplyNo());
                //设置指南页面
                libraryService.changeStatus(reviewOwnerQualificationInfo.getApplyNo(), AnnouncementEnum.QUALIFICATION_ACCEPTED);
            }
        } catch (Exception e) {
            //终止办件
            logger.error(e.getMessage(),e);
        }
    }


    /**
     * 自动审批用户资格信息
     *
     * @param reviewOwnerQualificationInfo the owner qualification info
     */
    @Override
    public void autoReviewOwnerQualification(OwnerQualification reviewOwnerQualificationInfo) {

        // 校验下状态是否能核查
        //  之后做一层reviewList转化成list的转化 便于判断
        //查询意向用户审批列表
        SelectStatementProvider selectStatement = SqlBuilder.select(ownerQualificationReviewDetail.allColumns())
                .from(ownerQualificationReviewDetail)
                .where(ownerQualificationReviewDetail.ownerQulificationId, isEqualTo(reviewOwnerQualificationInfo.getId()))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        List<OwnerQualificationReviewDetail> ownerQualificationReviewDetailList = ownerQualificationReviewDetailMapper.selectMany(selectStatement);


        //修复异常办件
        if (ownerQualificationReviewDetailList.isEmpty()) {
            logger.error("办件处理异常，办件信息：" + reviewOwnerQualificationInfo.getId() + "-" + reviewOwnerQualificationInfo.getApplyNo());
            if(StringUtils.isNotBlank(reviewOwnerQualificationInfo.getApplyNo())){
                recoverOwnerQualificationReviewDetail(reviewOwnerQualificationInfo);
            }else{
                //更新最终核查结果
                Date now = new Date();
                UpdateStatementProvider updateStatement = update(ownerQualification)
                        .set(ownerQualification.applyStatus).equalTo(OwnerQualificationStatusEnum.DENY.getStatus())
                        .set(ownerQualification.reason).equalTo("提交信息异常，请重新提交申请")
                        .set(ownerQualification.reviewTime).equalTo(now)
                        .set(ownerQualification.updatedTime).equalTo(now)
                        .set(ownerQualification.updatedUserId).equalTo("-1")
                        .set(ownerQualification.updatedUserName).equalTo("system")
                        .where(ownerQualification.id, isEqualTo(reviewOwnerQualificationInfo.getId()))
                        .and(ownerQualification.applyStatus,isEqualTo(0))
                        .build()
                        .render(RenderingStrategies.MYBATIS3);
                ownerQualificationMapper.update(updateStatement);
            }
            return;
        }

        //若是以旧换新，则需单独逻辑判断
        if(Objects.equals(reviewOwnerQualificationInfo.getQualificationType(), OwnerQualificationTypeEnum.EXCHANGE.getType())){
            autoReviewExchangeQualification(reviewOwnerQualificationInfo, ownerQualificationReviewDetailList);
            return;
        }

        //若是军官证，需要做特殊判断
        if (Integer.valueOf(1).equals(reviewOwnerQualificationInfo.getIsMilitaryOfficer())) {
            autoReviewMilitaryQualification(reviewOwnerQualificationInfo, ownerQualificationReviewDetailList);
            return;
        }


        Integer ownerStatus;
        //是否完成所有条件核查
        boolean finishReview = true;
        //最终结果是否拒绝
        boolean isDeny = false;
        //拒绝原因
        StringBuilder reason = new StringBuilder(StringUtils.EMPTY);
        //过期时间
        Date expireTime = null;

        for (OwnerQualificationReviewDetail reviewDetail : ownerQualificationReviewDetailList) {
            switch (reviewDetail.getReviewStatus()) {
                case 0:
                    //待核查
                    finishReview = false;
                    break;
                case 1:
                    //通过
                    break;
                case 2:      //拒绝
                case 3:     //未查询相关信息
                    isDeny = true;
                    if (reviewDetail.getReason() != null) {
                        reason.append(reviewDetail.getReason()).append(";");
                    }
                    break;
                default:
                    break;
            }
        }

        // 如果是测试环境，则默认走复核（方便测试）
        if ("test".equals(SpringContextUtil.getActiveProfile())) {
            if (reviewOwnerQualificationInfo.getProperty() == 1) {
                finishReview = true;
                isDeny = true;
            }
        }


        // 自动汇总结果
        if (finishReview) {
            if (isDeny) {
                //判断能否复核（只有一项被拒绝，切该项为信用未记录）
                boolean canReconsider = canQualificationReconsider(reviewOwnerQualificationInfo, ownerQualificationReviewDetailList);

                //异常数据处理，无视
                if (ownerQualificationReviewDetailList.isEmpty()) {
                    //存在保存办件失败的情况，
                    canReconsider = false;
                }

                // 如果是测试环境，则默认走复核（方便测试）
                if ("test".equals(SpringContextUtil.getActiveProfile())) {
                    if (reviewOwnerQualificationInfo.getProperty() == 1) {
                        canReconsider = true;
                    }
                }

                //审核不通过，且允许复核的情况
                if (canReconsider) {
                    //查询是否已经上传扫描件
                    final List<FileInfoDTO> fileInfo = fileService.getFileInfo(FileTypeEnum.QUALIFICATION_PROOF_COPY, reviewOwnerQualificationInfo.getId());
                    //如果已经上传复核扫描件，且能够复核，则直接复核中，办件任在受理中
                    if (fileInfo.size() > 0) {
                        ownerStatus = OwnerQualificationStatusEnum.RECONSIDERATION.getStatus();
                    } else {
                        String content = "经查询，您的新能源购车资格申请未通过，暂不符合新能源汽车专用牌照申领条件，原因为：" + reason
                                + " 您可选择：1、于30天内提交纳税记录等补正材料（需通过拟购品牌4S店上传，确保格式/内容正确）2、重新申请：拨打021-58812035或021-58810215，按接线员指引操作（工作日上午9:00-11:30，下午1:30-5:00）。";

                        //更新办件状态为补正状态（需要设置失效）
                        libraryService.supplementApply(reviewOwnerQualificationInfo.getApplyNo(), content);
                        expireTime = new Date(System.currentTimeMillis() + TimeUnit.DAYS.toMillis(30));
                        //设置指南页面
                        libraryService.changeStatus(reviewOwnerQualificationInfo.getApplyNo(), AnnouncementEnum.QUALIFICATION_SUPPLEMENT);

                        //发送短信通知
                        MessageUtil.sendMessage(reviewOwnerQualificationInfo.getMobilePhone(), content);

                        ownerStatus = OwnerQualificationStatusEnum.DENY.getStatus();
                    }
                }
                //审核不通过，且不允许复核= =
                else {

                    String content = "经查询，您的新能源购车资格申请未通过，暂不符合新能源汽车专用牌照申领条件，原因为：" + reason
                            + " 如有其他问题，可于工作日上午9:00-11:30，下午1:30-5:00咨询021-58812035。";

                    //更新办件状态为拒绝
                    libraryService.denyApply(reviewOwnerQualificationInfo.getApplyNo(), content);
                    //设置指南页面
                    libraryService.changeStatus(reviewOwnerQualificationInfo.getApplyNo(), AnnouncementEnum.QUALIFICATION_DENY);

                    //发送短信通知
                    MessageUtil.sendMessage(reviewOwnerQualificationInfo.getMobilePhone(), content);

                    ownerStatus = OwnerQualificationStatusEnum.DENY.getStatus();
                }
            }
            //审核通过
            else {
                //更新办件库状态为通过
                libraryService.approveApply(reviewOwnerQualificationInfo.getApplyNo());
                expireTime = new Date(System.currentTimeMillis() + TimeUnit.DAYS.toMillis(90));
                //设置指南页面
                libraryService.changeStatus(reviewOwnerQualificationInfo.getApplyNo(), AnnouncementEnum.QUALIFICATION_APPROVE);

                //发送短信通知
                String content = StringUtils.EMPTY;
                if(new Date().before(DateUtil.parse("2025-01-01 00:00:00", DateUtil.DATE_TYPE1))){
                    content = MESSAGE_CONTENT_2024;
                } else {
                    content = MESSAGE_CONTENT;
                }
                MessageUtil.sendMessage(reviewOwnerQualificationInfo.getMobilePhone(), content);
                ownerStatus = OwnerQualificationStatusEnum.APPROVE.getStatus();
            }
            //更新最终核查结果
            Date now = new Date();
            UpdateStatementProvider updateStatement = update(ownerQualification)
                    .set(ownerQualification.applyStatus).equalTo(ownerStatus)
                    .set(ownerQualification.reason).equalTo(reason.toString())
                    .set(ownerQualification.expireTime).equalToWhenPresent(expireTime)
                    .set(ownerQualification.reviewTime).equalTo(now)
                    .set(ownerQualification.updatedTime).equalTo(now)
                    .set(ownerQualification.updatedUserId).equalTo("-1")
                    .set(ownerQualification.updatedUserName).equalTo("system")
                    .where(ownerQualification.id, isEqualTo(reviewOwnerQualificationInfo.getId()))
                    .and(ownerQualification.applyStatus,isEqualTo(0))
                    .build()
                    .render(RenderingStrategies.MYBATIS3);
            ownerQualificationMapper.update(updateStatement);
        }
    }


    /**
     * 自动审核以旧换新资质
     * @param reviewOwnerQualificationInfo 以旧换新资质基本信息
     * @param ownerQualificationReviewDetailList 以旧换新各部门审批明细
     */
    private void autoReviewExchangeQualification(OwnerQualification reviewOwnerQualificationInfo, List<OwnerQualificationReviewDetail> ownerQualificationReviewDetailList){
        //处理异常办件
        if(ownerQualificationReviewDetailList == null ||ownerQualificationReviewDetailList.isEmpty()){
            log.error("以旧换新资质信息异常:" + JSON.toJSONString(reviewOwnerQualificationInfo));
            return;
        }

        //校验是否存在未审核完成的记录
        boolean notFinishReview = ownerQualificationReviewDetailList.stream()
                .anyMatch(reviewDetail -> Objects.equals(reviewDetail.getReviewStatus(), OwnerQualificationReviewStatusEnum.PENDING.getStatus()));
        //未完成全部核查 - 不做任何处理
        if(notFinishReview){
            return;
        }


        //是否审批拒绝
        boolean isDeny = false;
        StringBuilder reason = new StringBuilder(StringUtils.EMPTY);
        for (OwnerQualificationReviewDetail reviewDetail : ownerQualificationReviewDetailList) {
            switch (reviewDetail.getReviewStatus()) {
                case 2:      //拒绝
                case 3:     //未查询相关信息
                    isDeny = true;
                    if (reviewDetail.getReason() != null) {
                        reason.append(reviewDetail.getReason()).append(";");
                    }
                    break;
                default:
                    break;
            }
        }
        Integer applyStatus = isDeny ? OwnerQualificationStatusEnum.DENY.getStatus(): OwnerQualificationStatusEnum.APPROVE.getStatus();

        //针对审核拒绝的记录，判断能否复议
        boolean canReconsider = true;
        if(isDeny){
            //如果是现役军人
            if(Objects.equals(reviewOwnerQualificationInfo.getIsMilitaryOfficer(), 1)){
                for (OwnerQualificationReviewDetail reviewDetail : ownerQualificationReviewDetailList) {
                    switch (reviewDetail.getReviewStatus()) {
                        case 2:
                            //拒绝(不校验社保）
                            if (!Objects.equals(reviewDetail.getReviewType(), OwnerQualificationReviewTypeEnum.RESIDENCE.getType())
                                && !Objects.equals(reviewDetail.getReviewType(), OwnerQualificationReviewTypeEnum.SOCIAL.getType())
                                && !Objects.equals(reviewDetail.getReviewType(), OwnerQualificationReviewTypeEnum.TRAFFIC.getType())
                            ){
                                canReconsider = false;
                            }
                            break;
                        case 3:
                            //未查询相关信息（只校验公安）
                            if (Objects.equals(reviewDetail.getReviewType(), OwnerQualificationReviewTypeEnum.DRIVER.getType())) {
                                canReconsider = false;
                            }
                            break;
                        default:
                            break;
                    }
                }
                if(canReconsider){
                    applyStatus = OwnerQualificationStatusEnum.RECONSIDERATION.getStatus();
                }
            }
            //普通用户
            else{
                //信用、驾照、居住证被审核拒绝后，直接无法复议
                boolean noReconsider = ownerQualificationReviewDetailList.stream().filter(reviewDetail ->
                                Objects.equals(OwnerQualificationReviewStatusEnum.DENY.getStatus(), reviewDetail.getReviewStatus())
                                        || Objects.equals(OwnerQualificationReviewStatusEnum.NOT_FOUND.getStatus(), reviewDetail.getReviewStatus()))
                        .anyMatch(reviewDetail -> Objects.equals(OwnerQualificationReviewTypeEnum.DRIVER.getType(), reviewDetail.getReviewType())
                                || Objects.equals(OwnerQualificationReviewTypeEnum.RESIDENCE.getType(), reviewDetail.getReviewType())
                                || Objects.equals(OwnerQualificationReviewTypeEnum.CREDIT.getType(), reviewDetail.getReviewType()));
                if(noReconsider){
                    canReconsider = false;
                }
            }
        }


        //更新最终结果
        Date now = new Date();
        Date expireTime;
        //如果是2025年之前提交的，则有效期至2024-12-31
        if(now.before(DateUtil.parse("2025-01-01 00:00:00", DateUtil.DATE_TYPE1))) {
            expireTime = DateUtil.parse("2024-12-31", DateUtil.DATE_TYPE3);
        }else{
            //有效期为90天
            expireTime = new Date(TimeUnit.DAYS.toMillis(90) + now.getTime());
        }
        UpdateStatementProvider updateStatement = update(ownerQualification)
                .set(ownerQualification.applyStatus).equalTo(applyStatus)
                .set(ownerQualification.reason).equalTo(reason.toString())
                .set(ownerQualification.expireTime).equalToWhenPresent(canReconsider ? expireTime : null)
                .set(ownerQualification.reviewTime).equalTo(now)
                .set(ownerQualification.updatedTime).equalTo(now)
                .set(ownerQualification.updatedUserId).equalTo("-1")
                .set(ownerQualification.updatedUserName).equalTo("system")
                .where(ownerQualification.id, isEqualTo(reviewOwnerQualificationInfo.getId()))
                .and(ownerQualification.applyStatus,isEqualTo(0))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        ownerQualificationMapper.update(updateStatement);
    }



    /**
     * 自动核查军官证信息
     *
     * @param ownerQualificationReviewDetailList 用户资格各部门核查结果
     */
    private void autoReviewMilitaryQualification(OwnerQualification reviewOwnerQualificationInfo, List<OwnerQualificationReviewDetail> ownerQualificationReviewDetailList) {

        boolean finishReview = true;
        boolean isDeny = false;
        StringBuilder reason = new StringBuilder(StringUtils.EMPTY);

        for (OwnerQualificationReviewDetail reviewDetail : ownerQualificationReviewDetailList) {
            switch (reviewDetail.getReviewStatus()) {
                case 0:
                    //待核查
                    finishReview = false;
                    break;
                case 1:
                    //通过
                    break;
                case 2:
                    //拒绝(不校验社保）
                    if (reviewDetail.getReviewType() != 2) {
                        isDeny = true;
                    }
                    reason.append(reviewDetail.getReason()).append(";");
                    break;
                case 3:
                    //未查询相关信息（只校验公安）
                    if (reviewDetail.getReviewType() == 0) {
                        isDeny = true;
                    }
                    reason.append(reviewDetail.getReason()).append(";");
                    break;
                default:
                    break;
            }
        }

        if (finishReview) {
            if (isDeny) {
                //更新办件状态为拒绝
                libraryService.denyApply(reviewOwnerQualificationInfo.getApplyNo(), reason.toString());
                //设置指南页面
                libraryService.changeStatus(reviewOwnerQualificationInfo.getApplyNo(), AnnouncementEnum.QUALIFICATION_DENY);

                //发送短信通知
                String content = "经查询，您的新能源购车资格申请未通过，暂不符合新能源汽车专用牌照申领条件，原因为：" + reason + ";如有其他问题，可于工作日上午9:00-11:30，下午1:30-5:00咨询021-58812035。";
                MessageUtil.sendMessage(reviewOwnerQualificationInfo.getMobilePhone(), content);
            }
            //更新最终核查结果
            Date now = new Date();
            UpdateStatementProvider updateStatement = update(ownerQualification)
                    .set(ownerQualification.applyStatus).equalTo(isDeny ? OwnerQualificationStatusEnum.DENY.getStatus() : OwnerQualificationStatusEnum.RECONSIDERATION.getStatus())
                    .set(ownerQualification.reason).equalTo(reason.toString())
                    .set(ownerQualification.reviewTime).equalTo(now)
                    .set(ownerQualification.updatedTime).equalTo(now)
                    .set(ownerQualification.updatedUserId).equalTo(Const.ADMIN_ID)
                    .set(ownerQualification.updatedUserName).equalTo(Const.ADMIN_NAME)
                    .where(ownerQualification.id, isEqualTo(reviewOwnerQualificationInfo.getId()))
                    .build()
                    .render(RenderingStrategies.MYBATIS3);
            ownerQualificationMapper.update(updateStatement);
        }
    }


    /**
     * 判断复核条件
     *
     * @param reviewOwnerQualificationInfo
     * @return
     */
    private Boolean canQualificationReconsider(OwnerQualification reviewOwnerQualificationInfo) {
        //TODO  有问题 重写
        SelectStatementProvider selectStatement = SqlBuilder.select(ownerQualificationReviewDetail.allColumns())
                .from(ownerQualificationReviewDetail)
                .where(ownerQualificationReviewDetail.ownerQulificationId, isEqualTo(reviewOwnerQualificationInfo.getId()))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        List<OwnerQualificationReviewDetail> ownerQualificationReviewDetailList = ownerQualificationReviewDetailMapper.selectMany(selectStatement);
        return canQualificationReconsider(reviewOwnerQualificationInfo, ownerQualificationReviewDetailList);
    }


    /**
     * 判断复核条件
     *
     * @param reviewOwnerQualificationInfo
     * @param ownerQualificationReviewDetailList
     * @return
     */
    public static Boolean canQualificationReconsider(OwnerQualification reviewOwnerQualificationInfo, List<OwnerQualificationReviewDetail> ownerQualificationReviewDetailList) {
        Boolean result = true;
        //私人 才能复核
        if (reviewOwnerQualificationInfo.getProperty() == 1) {
            if (reviewOwnerQualificationInfo.getApplyTime().getTime() < DateUtil.parse("20210301", DateUtil.DATE_TYPE4).getTime()) {
                return false;
            }
            // 由于办件库异常导致数据都没有的不能复核
            if (ownerQualificationReviewDetailList.isEmpty()) {
                return false;
            }

            for (OwnerQualificationReviewDetail reviewDetail : ownerQualificationReviewDetailList) {
                //信用【未完成】或【不通过】不能复核,居民身份证不能复核
                if (reviewDetail.getReviewType() == 1 && (reviewDetail.getReviewStatus() == 0 || reviewDetail.getReviewStatus() == 2)) {
                    return false;
                }
                if (reviewDetail.getReviewType() == 1 && reviewDetail.getReviewStatus() == 3 && reviewOwnerQualificationInfo.getAuthType() == 1) {
                    return false;
                }
                //驾照不通过不能复核
                if (reviewDetail.getReviewType() == 0 && reviewDetail.getReviewStatus() != 1) {
                    return false;
                }
                // 居住证
                if (reviewDetail.getReviewType() == 3 && reviewDetail.getReviewStatus() != 1) {
                    return false;
                }
                // 交委
                if (reviewDetail.getReviewType() == 4 && reviewDetail.getReviewStatus() != 1) {
                    return false;
                }
                //社保未完成 能复核
                if (reviewDetail.getReviewType() == 2 && (reviewDetail.getReviewStatus() == 0)) {
                    return false;
                }
            }
        } else {
            for (OwnerQualificationReviewDetail reviewDetail : ownerQualificationReviewDetailList) {
                //信用不为【审核通过】则不能复核
                if(OwnerQualificationReviewTypeEnum.CREDIT.getType().equals(reviewDetail.getReviewType())
                        && !OwnerQualificationReviewStatusEnum.APPROVE.getStatus().equals(reviewDetail.getReviewStatus())){
                    return false;
                }

                //社保不为【审核不通过】，则不能复核
                if(OwnerQualificationReviewTypeEnum.SOCIAL.getType().equals(reviewDetail.getReviewType())
                    && !OwnerQualificationReviewStatusEnum.DENY.getStatus().equals(reviewDetail.getReviewStatus())){
                    return false;
                }
            }
        }
        return result;
    }

    /**
     * 自动分发并核查资格信息（无驾照信息）
     *
     * @param assignOwnerQualification 指派的用户资格信息
     */
    private void autoAssignQualificationReview(OwnerQualification assignOwnerQualification) {
        autoAssignQualificationReview(assignOwnerQualification, null);
    }

    /**
     * 自动分发并核查资格信息
     *
     * @param assignOwnerQualification 指派的用户资格信息
     * @param driverLicense            驾驶证信息
     */
    private void autoAssignQualificationReview(OwnerQualification assignOwnerQualification, DriverLicenseDTO driverLicense) {

        //以旧换新资质申请 - 此处默认以旧换新都为个人提交申请
        if(Objects.equals(assignOwnerQualification.getQualificationType(), OwnerQualificationTypeEnum.EXCHANGE.getType())){
            //查询驾照信息
            driverService.review(assignOwnerQualification.getId(), driverLicense);

            //查询信用信息
            creditService.review(assignOwnerQualification.getId(), assignOwnerQualification.getProperty(), assignOwnerQualification.getName(), assignOwnerQualification.getAuthId());

            //大牌额度
            trafficCommitteeService.reviewLicenseLimit(assignOwnerQualification.getId());

            //查询居住证【外省市（不包括港澳台）】，且非军官
            if (assignOwnerQualification.getHouseholdRegistrationType() == 1 && assignOwnerQualification.getIsMilitaryOfficer() == 0) {
                residencePermitService.review(assignOwnerQualification.getId(),assignOwnerQualification.getName(), assignOwnerQualification.getAuthId());
            }

            //如果非上海市户籍，则查询社保信息
            if (assignOwnerQualification.getHouseholdRegistrationType() != 0) {
                newSocialService.review(assignOwnerQualification.getId(), assignOwnerQualification.getName(), assignOwnerQualification.getAuthType(), assignOwnerQualification.getAuthId());
            }

            //查询以旧换新车辆的额度性质
            trafficCommitteeService.reviewExchangeLimit(assignOwnerQualification.getId());

        }
        //普通购车资质
        else{
            //判断是否是特殊人才
            boolean isSpecialOwner = ownerQualificationSpecialService.isSpecialOwner(assignOwnerQualification.getAuthId());
            if(isSpecialOwner) {
                logger.warn("该用户：{}为特殊人才，略过常规校验。", assignOwnerQualification.getAuthId());
                //查询驾照信息-仅查询名下是否有新能源车
                if (assignOwnerQualification.getProperty() != 2) {
                    driverService.review(assignOwnerQualification.getId(), driverLicense, true);
                }
                return;
            }

            //查询驾照信息（除法人）
            if (assignOwnerQualification.getProperty() != 2) {
                driverService.review(assignOwnerQualification.getId(), driverLicense);
            }

            //查询信用信息
            creditService.review(assignOwnerQualification.getId(), assignOwnerQualification.getProperty(), assignOwnerQualification.getName(), assignOwnerQualification.getAuthId());

            //非法人都需要判断大牌额度
            if(assignOwnerQualification.getProperty() != 2){
                trafficCommitteeService.reviewLicenseLimit(assignOwnerQualification.getId());
            }

            //查询居住证（外省市（不包括港澳台）），除军官
            if (assignOwnerQualification.getProperty() != 2 && assignOwnerQualification.getHouseholdRegistrationType() == 1 && assignOwnerQualification.getIsMilitaryOfficer() == 0) {
                residencePermitService.review(assignOwnerQualification.getId(),assignOwnerQualification.getName(), assignOwnerQualification.getAuthId());
            }

            //如果是法人，或者外省市用户，则查询社保信息
            if (assignOwnerQualification.getProperty() == 2 || assignOwnerQualification.getHouseholdRegistrationType() != 0) {
                newSocialService.review(assignOwnerQualification.getId(), assignOwnerQualification.getName(), assignOwnerQualification.getAuthType(), assignOwnerQualification.getAuthId());
            }

            // 如果是企业用户，则直接自动核查，当场返回结果
            if (assignOwnerQualification.getProperty() == 2) {
                autoReviewOwnerQualification(assignOwnerQualification);
            }
        }
    }

    /**
     * 保存用户资格信息
     *
     * @param saveOwnerQualification the owner qualification info
     * @return owner qualification id
     */
    private Long saveOwnerQualificationInfo(OwnerQualification saveOwnerQualification, String createdUserId, String createdUserName) {

        //混动车自2023年后拒绝申请
        if(ObjectUtils.nullSafeEquals(saveOwnerQualification.getVehicleModelType(),2)) {
            Date validDate = DateUtil.parse("2023-01-01", DateUtil.DATE_TYPE3);
            if (new Date().after(validDate)) {
                throw new ServiceException("根据《上海市鼓励购买和使用新能源汽车实施办法》（沪府办规〔2021〕3号）相关要求，自2023年1月1日起，不再接受插电式混合动力车辆申请。");
            }
        }

        Date now = new Date();
        saveOwnerQualification.setApplyTime(now);
        saveOwnerQualification.setCreatedTime(now);
        saveOwnerQualification.setCreatedUserId(createdUserId);
        saveOwnerQualification.setCreatedUserName(createdUserName);
        saveOwnerQualification.setUpdatedTime(now);
        saveOwnerQualification.setUpdatedUserId(createdUserId);
        saveOwnerQualification.setUpdatedUserName(createdUserName);
        ownerQualificationMapper.insertSelective(saveOwnerQualification);

        // 保存信息到需要推送的数据表owner_traffic_committee_qualification里
        if (saveOwnerQualification.getProperty() != 2) {
            OwnerTrafficCommitteeQualification ownerTrafficCommitteeQualification = new OwnerTrafficCommitteeQualification();
            ownerTrafficCommitteeQualification.setOwnerQualificationId(saveOwnerQualification.getId());
            ownerTrafficCommitteeQualification.setCreatedUserId(createdUserId);
            ownerTrafficCommitteeQualification.setCreatedUserName(createdUserName);
            ownerTrafficCommitteeQualification.setCreatedTime(now);
            ownerTrafficCommitteeQualification.setUpdatedUserId(createdUserId);
            ownerTrafficCommitteeQualification.setUpdatedUserName(createdUserName);
            ownerTrafficCommitteeQualification.setUpdatedTime(now);
            ownerTrafficCommitteeQualificationMapper.insertSelective(ownerTrafficCommitteeQualification);
        }

        // 保存推送以旧换新额度表中，等待定时任务触发
        if(Objects.equals(saveOwnerQualification.getQualificationType(), OwnerQualificationTypeEnum.EXCHANGE.getType())){
            OwnerTrafficCommitteeExchange ownerTrafficCommitteeExchange = new OwnerTrafficCommitteeExchange();
            ownerTrafficCommitteeExchange.setOwnerQualificationId(saveOwnerQualification.getId());
            ownerTrafficCommitteeExchange.setCreatedTime(now);
            ownerTrafficCommitteeExchange.setUpdatedTime(now);
            ownerTrafficCommitteeExchangeMapper.insertSelective(ownerTrafficCommitteeExchange);
        }

        //保存操作日志
        SaveOperateLogDTO saveOperateLogDTO = SaveOperateLogDTO.builder()
                .userOperateTypeEnum(UserOperateTypeEnum.SAVE_OWNER_QUALIFICATION)
                .ownerQualificationId(saveOwnerQualification.getId())
                .operateUserId(createdUserId)
                .operateUserName(createdUserName)
                .build();
        userOperateLogService.saveOperateLog(saveOperateLogDTO);
        return saveOwnerQualification.getId();
    }

    /**
     * 获取未结束的意向用户信息
     *
     * @param authType 证件类型
     * @param authId   证件号码
     * @return OwnerQualification
     */
    @Override
    public OwnerQualification getUnfinishedQualification(Integer authType, String authId) {
        SelectStatementProvider selectStatement = select(ownerQualification.allColumns())
                .from(ownerQualification)
                .where()
                .and(ownerQualification.authType, isEqualTo(authType))
                .and(ownerQualification.authId, isEqualTo(authId))
                .and(ownerQualification.status, isEqualTo(1))
                //未核查完毕的（未通过,未拒绝的）
                .and(ownerQualification.applyStatus, isNotIn(OwnerQualificationStatusEnum.DENY.getStatus(), OwnerQualificationStatusEnum.APPROVE.getStatus())
                        //核查拒绝但是未完成复核的
                        , or(ownerQualification.applyStatus, isEqualTo(OwnerQualificationStatusEnum.DENY.getStatus()), and(ownerQualification.expireTime, isGreaterThanOrEqualTo(new Date())))
                        //核查通过但是未过期的
                        , or(ownerQualification.applyStatus, isEqualTo(OwnerQualificationStatusEnum.APPROVE.getStatus()), and(ownerQualification.expireTime, isGreaterThanOrEqualTo(new Date())))
                )
                .and(ownerQualification.qualificationType, isEqualTo(OwnerQualificationTypeEnum.NORMAL.getType()))
                .limit(1)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        Optional<OwnerQualification> optionalOwnerQualification = ownerQualificationMapper.selectOne(selectStatement);
        return optionalOwnerQualification.orElse(null);
    }

    @Override
    public List<OwnerQualification> queryOwnerQualification(Integer authType, String authId, Integer qualificationType) {
        SelectStatementProvider selectStatement = select(ownerQualification.allColumns())
                .from(ownerQualification)
                .where()
                .and(ownerQualification.authType, isEqualTo(authType))
                .and(ownerQualification.authId, isEqualTo(authId))
                .and(ownerQualification.qualificationType, isEqualToWhenPresent(qualificationType))
                .and(ownerQualification.status,isEqualTo(1))
                .orderBy(ownerQualification.id.descending())
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return ownerQualificationMapper.selectMany(selectStatement);
    }

    @Override
    public QueryOwnerSocialDetailResponse queryOwnerSocialDetail(QueryOwnerSocialDetailDTO queryOwnerSocialDetailDTO) {
        QueryOwnerSocialDetailResponse response = new QueryOwnerSocialDetailResponse();
        String dateStart = StringUtils.EMPTY;
        if(queryOwnerSocialDetailDTO.getStartTime() != null){
            dateStart = DateUtil.format(queryOwnerSocialDetailDTO.getStartTime(), DateUtil.DATE_TYPE6);
        }
        String dateEnd = StringUtils.EMPTY;
        if(queryOwnerSocialDetailDTO.getEndTime() != null){
            dateEnd = DateUtil.format(queryOwnerSocialDetailDTO.getEndTime(), DateUtil.DATE_TYPE6);
        }

        NewSocialServiceImpl.SocialResponse taxationResponse = newSocialService.reviewSocialFromTaxation2025(
                queryOwnerSocialDetailDTO.getName(), queryOwnerSocialDetailDTO.getAuthType(), queryOwnerSocialDetailDTO.getAuthId(),
                dateStart, dateEnd);
        response.setTaxationResponse(taxationResponse);


        NewSocialServiceImpl.SocialResponse socialResponse = newSocialService.reviewSocialFromSocialAPI2025(
                queryOwnerSocialDetailDTO.getName(), queryOwnerSocialDetailDTO.getAuthType(), queryOwnerSocialDetailDTO.getAuthId(),
                dateStart, dateEnd);
        response.setSocialResponse(socialResponse);
        return response;
    }


    /**
     * 判断是否有未结束的意向用户记录
     *
     * @param authTypeValue 证件类型
     * @param authIdValue   证件号码
     * @return true:存在未结束的意向用户记录  false:不存在
     */
    private boolean existUnfinishedQualification(Integer authTypeValue, String authIdValue) {
        return getUnfinishedQualification(authTypeValue, authIdValue) != null;
    }


    /**
     * 绑定用户购车资格相关办件
     */
    private void bindOwnerQualificationApply(String applyNo, Long ownerQualificationId) {
        UpdateStatementProvider updateStatement = SqlBuilder.update(ownerQualification)
                .set(ownerQualification.applyNo).equalTo(applyNo)
                .where(ownerQualification.id, isEqualTo(ownerQualificationId))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        ownerQualificationMapper.update(updateStatement);
    }

    /**
     * 复核意向用户
     *
     * @param ownerQualificationId the owner qualification id
     */
    private void reconsiderQualification(Long ownerQualificationId, String updatedUserId, String updatedUserName, String reconsiderOrgName) {
        //复核意向用户
        UpdateStatementProvider updateStatement = SqlBuilder.update(ownerQualification)
                .set(ownerQualification.applyStatus).equalTo(OwnerQualificationStatusEnum.RECONSIDERATION.getStatus())
                .set(ownerQualification.updatedTime).equalTo(new Date())
                .set(ownerQualification.updatedUserId).equalTo(updatedUserId)
                .set(ownerQualification.updatedUserName).equalTo(updatedUserName)
                .set(ownerQualification.reconsiderationImg).equalTo(reconsiderOrgName)
                .where(ownerQualification.id, isEqualTo(ownerQualificationId))
                .and(ownerQualification.applyStatus, isIn(OwnerQualificationStatusEnum.REVIEWING.getStatus(), OwnerQualificationStatusEnum.DENY.getStatus()))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        ownerQualificationMapper.update(updateStatement);

        //保存操作日志
        SaveOperateLogDTO saveOperateLogDTO = SaveOperateLogDTO.builder()
                .userOperateTypeEnum(UserOperateTypeEnum.RECONSIDER_OWNER_QUALIFICATION)
                .ownerQualificationId(ownerQualificationId)
                .operateUserId(updatedUserId)
                .operateUserName(updatedUserName)
                .build();
        userOperateLogService.saveOperateLog(saveOperateLogDTO);
    }

    /**
     * 根据 ID 查询意向用户列表
     *
     * @param ownerQualificationIds 意向用户ID
     * @return List<OwnerQualification>
     */
    public List<OwnerQualification> queryOwnerQualifications(List<Long> ownerQualificationIds) {
        if (ownerQualificationIds.isEmpty()) {
            return new ArrayList<>();
        }
        SelectStatementProvider selectStatement = SqlBuilder.select(ownerQualification.allColumns())
                .from(ownerQualification)
                .where(ownerQualification.id, isIn(ownerQualificationIds))
                .and(ownerQualification.status,isEqualTo(1))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return ownerQualificationMapper.selectMany(selectStatement);
    }


    /**
     * 公安导入Excel自动核查
     */
    private Integer trafficAutoApprove(OwnerQualification ownerQualification, OwnerTrafficQualification userFeedbackDTO) {
        StringBuilder reason = new StringBuilder();
        boolean isSuccess = true;
        if (!Integer.valueOf(1).equals(userFeedbackDTO.getHasDriverLicense())) {
            // 无驾照
            reason.append("未查询到驾照信息（驾驶证发证单位及核发地需保持一致。如您刚更新驾照信息，请在3个工作日后再查询，如您刚考取驾照，请在7个工作日后再查询。）");
            userFeedbackDTO.setReason(reason.toString());
            // 查无此人, 直接返回。
            return 3;
        }
        boolean isDriverLicenseAbnormal = false;
        if (!Integer.valueOf(1).equals(userFeedbackDTO.getDriverLicenseNoExpire())) {
            // 驾照是否过期
            reason.append("驾驶证已过期");
            isSuccess = false;
            isDriverLicenseAbnormal = true;
        }
        if (!Integer.valueOf(1).equals(userFeedbackDTO.getDriverLicenseIsValidity())) {
            // 驾照是否有效
            if(isDriverLicenseAbnormal){
                reason.append("，");
            }
            reason.append("驾驶证已失效");
            isSuccess = false;
            isDriverLicenseAbnormal = true;
        }

        if(isDriverLicenseAbnormal){
            reason.append("（驾驶证发证单位及核发地需保持一致。如您刚更新驾照信息，请在3个工作日后再查询）;");
        }

        if (!Integer.valueOf(1).equals(userFeedbackDTO.getDriverLicenseHasViolation())) {
            // 驾照违章次数
            reason.append("近一年违章数量大于等于5次;");
            isSuccess = false;
        }
        if (!Integer.valueOf(1).equals(userFeedbackDTO.getHasPunish())) {
            // 有无处罚
            reason.append("近一年产生过处罚;");
            isSuccess = false;
        }
        //以旧换新不用查询名下有无新能源车
        if(Objects.equals(OwnerQualificationTypeEnum.NORMAL.getType(), ownerQualification.getQualificationType())) {
            if (!Integer.valueOf(1).equals(userFeedbackDTO.getHasNewEnergyVehicle())) {
                reason.append("名下有新能源汽车（如您刚将新能源汽车转出，请在3个工作日后再查询）;");
                isSuccess = false;
            }
        }
        if (!isSuccess) {
            userFeedbackDTO.setReason(reason.substring(0, reason.length() - 1));
            // 核查拒绝
            return 2;
        }
        // 核查成功
        return 1;
    }

    private Integer tranHasNewEnergyVehicle(Boolean hasNewEnergyVehicle) {
        if (hasNewEnergyVehicle) {
            return 0;
        }
        return 1;
    }


    @Override
    public SubmitExchangeQualificationResponse submitExchangeQualification(SubmitExchangeQualificationDTO submitExchangeQualificationDTO) {
        //校验车架号格式
        if(!VinFormatVerificationUtil.check(submitExchangeQualificationDTO.getExchangeVin())){
            throw new ServiceException("车辆识别代码格式错误");
        }
        //校验身份证号格式
        if(Objects.equals(submitExchangeQualificationDTO.getAuthType(), AuthTypeEnum.IDENTIFICATION_CARD.getType())
                && !IdCardUtil.isIDNumber(submitExchangeQualificationDTO.getAuthId())){
            throw new ServiceException("证件号格式错误");
        }
        //车牌号校验
        if(!submitExchangeQualificationDTO.getExchangeVehicleNo().startsWith("沪")){
            throw new ServiceException("该牌照暂不支持以旧换新政策");
        }
        if(!LicensePlateValidator.isValidLicensePlate(submitExchangeQualificationDTO.getExchangeVehicleNo())){
            throw new ServiceException("牌照号格式错误");
        }

        //校验上海市用户的户籍信息
        if(submitExchangeQualificationDTO.getHouseholdRegistrationType() == 0) {
            boolean isLocalHousehold = householdRegistrationService.isLocalHousehold(submitExchangeQualificationDTO.getName(), submitExchangeQualificationDTO.getAuthId());
            if (!isLocalHousehold) {
                log.error("用户：{}，户籍信息填写异常", submitExchangeQualificationDTO.getAuthId());
                throw new ServiceException("请确认户籍信息是否填写正确");
            }
        }


        //针对普通的私人用户，需要校验户籍信息
        if(submitExchangeQualificationDTO.getIsMilitaryOfficer() == 0
                && Objects.equals(AuthTypeEnum.IDENTIFICATION_CARD.getType(), submitExchangeQualificationDTO.getAuthType())){

            //校验外省市居住证信息
            if(submitExchangeQualificationDTO.getHouseholdRegistrationType() == 1){
                //校验是否拥有居住证
                boolean hasResidencePermit = residencePermitService.hasResidencePermit(submitExchangeQualificationDTO.getName(), submitExchangeQualificationDTO.getAuthId());
                if(!hasResidencePermit){
                    throw new ServiceException("外省市（非港澳台及其他外籍人士）必须持有有效上海市居住证才可办理");
                }
            }
        }

        //重新提交以旧换新资质 - 先逻辑删除原以旧换新数据，后创建一条新的以旧换新申请
        if(submitExchangeQualificationDTO.getId() != null){
            OwnerQualification exchangeQualification = checkExchangeQualification(submitExchangeQualificationDTO.getId(), submitExchangeQualificationDTO.getAuthType(), submitExchangeQualificationDTO.getAuthId());
            //校验当前状态能否重新提交
            if(Objects.equals(OwnerQualificationStatusEnum.APPROVE.getStatus(), exchangeQualification.getApplyStatus())) {
                if (exchangeQualification.getExpireTime() != null && exchangeQualification.getExpireTime().compareTo(new Date()) > 0) {
                    throw new ServiceException("当前状态无法重新提交");
                }
            }
            else if(Objects.equals(OwnerQualificationStatusEnum.DENY.getStatus(), exchangeQualification.getApplyStatus())){
                //根据有效期判断当前申请是否处于复核中状态
                if (exchangeQualification.getExpireTime() != null && exchangeQualification.getExpireTime().compareTo(new Date()) > 0) {
                    throw new ServiceException("请先申请放弃复核");
                }
            }
            else{
                throw new ServiceException("当前状态无法重新提交以旧换新申请");
            }

            //逻辑删除以旧换新申请信息
            deleteExchangeQualification(submitExchangeQualificationDTO.getId());

            //保存操作日志
            SaveOperateLogDTO saveOperateLogDTO = SaveOperateLogDTO.builder()
                    .userOperateTypeEnum(UserOperateTypeEnum.RESUME_EXCHANGE_QUALIFICATION)
                    .ownerQualificationId(exchangeQualification.getId())
                    .operateUserId(submitExchangeQualificationDTO.getOperatorId())
                    .operateUserName(submitExchangeQualificationDTO.getOperatorName())
                    .build();
            userOperateLogService.saveOperateLog(saveOperateLogDTO);
        }

        //校验是否已经提交过该车辆的以旧换新申请
        boolean existExchangeQualification = existExchangeQualification(null, submitExchangeQualificationDTO.getAuthType()
                , submitExchangeQualificationDTO.getAuthId(), submitExchangeQualificationDTO.getExchangeVin());
        if(existExchangeQualification){
            throw new ServiceException("您已经提交过该车辆的以旧换新申请，请勿重复提交");
        }

        //保存以旧换新资格基本信息
        OwnerQualification saveOwnerQualification = new OwnerQualification();
        saveOwnerQualification.setName(submitExchangeQualificationDTO.getName());
        saveOwnerQualification.setAuthId(submitExchangeQualificationDTO.getAuthId());
        saveOwnerQualification.setAuthType(submitExchangeQualificationDTO.getAuthType());
        saveOwnerQualification.setHouseholdRegistrationType(submitExchangeQualificationDTO.getHouseholdRegistrationType());
        saveOwnerQualification.setProperty(1);
        //是否是现役军人
        saveOwnerQualification.setIsMilitaryOfficer(submitExchangeQualificationDTO.getIsMilitaryOfficer());
        //资质类型：0:普通购车资质 1:以旧换新资格
        saveOwnerQualification.setQualificationType(OwnerQualificationTypeEnum.EXCHANGE.getType());
        saveOwnerQualification.setExchangeVin(submitExchangeQualificationDTO.getExchangeVin());
        saveOwnerQualification.setExchangeVehicleNo(submitExchangeQualificationDTO.getExchangeVehicleNo());
        saveOwnerQualification.setExchangeVehicleType(submitExchangeQualificationDTO.getExchangeVehicleType());
        Long ownerQualificationId = this.saveOwnerQualificationInfo(saveOwnerQualification, submitExchangeQualificationDTO.getOperatorId(), submitExchangeQualificationDTO.getOperatorName());

        //如果是军官证
        if(submitExchangeQualificationDTO.getIsMilitaryOfficer() == 1){
            //清除历史上传的扫描件
            fileService.clearFile(FileTypeEnum.MILITARY_OFFICER_COPY, ownerQualificationId, submitExchangeQualificationDTO.getOperatorId(), submitExchangeQualificationDTO.getOperatorName());
            // 保存军官证件照扫描件信息
            fileService.saveFileInfo(FileTypeEnum.MILITARY_OFFICER_COPY, submitExchangeQualificationDTO.getFile(), ownerQualificationId
                    , submitExchangeQualificationDTO.getOperatorId(), submitExchangeQualificationDTO.getOperatorName());
        }

        //组装驾照信息
        DriverLicenseDTO driverLicense = DriverLicenseDTO.builder()
                .driverLicenseCode(submitExchangeQualificationDTO.getDriverLicenseCode())
                .driverLicenseIssuingOrganization(submitExchangeQualificationDTO.getDriverLicenseIssuingOrganization())
                .driverLicenseIssuingPlace(submitExchangeQualificationDTO.getDriverLicenseIssuingPlace())
                .driverFileNo(submitExchangeQualificationDTO.getDriverFileNo())
                .build();
        //分发资质信息到各部委查询校验【主要逻辑！！！】
        autoAssignQualificationReview(saveOwnerQualification, driverLicense);

        return new SubmitExchangeQualificationResponse(ownerQualificationId);
    }


    /**
     * 逻辑删除以旧换新资质申请
     * @param ownerQualificationId 删除的ID
     */
    private void deleteExchangeQualification(Long ownerQualificationId){
        if(ownerQualificationId == null){
            return;
        }
        OwnerQualification updateOwnerQualification = new OwnerQualification();
        updateOwnerQualification.setId(ownerQualificationId);
        updateOwnerQualification.setStatus(0);
        updateOwnerQualification.setUpdatedTime(new Date());
        updateOwnerQualification.setUpdatedUserId(Const.ADMIN_ID);
        updateOwnerQualification.setUpdatedUserName(Const.SMALL_PROGRAM);
        ownerQualificationMapper.updateByPrimaryKeySelective(updateOwnerQualification);
    }

    @Override
    public SearchExchangeQualificationListResponse searchExchangeQualificationList(SearchExchangeQualificationListDTO searchExchangeQualificationListDTO) {
        List<SearchExchangeQualificationData> rows = new ArrayList<>();

        String authId = searchExchangeQualificationListDTO.getAuthId();
        Integer authType = searchExchangeQualificationListDTO.getAuthType();
        if(StringUtils.isBlank(authId) || authType == null){
            return new SearchExchangeQualificationListResponse(0L, rows);
        }

        //根据证件号、证件类型查询以旧换新资格申请列表
        SelectStatementProvider selectStatement = select(ownerQualification.allColumns())
                .from(ownerQualification)
                .where(ownerQualification.authId, isEqualTo(authId))
                .and(ownerQualification.authType,isEqualTo(authType))
                .and(ownerQualification.qualificationType, isEqualTo(OwnerQualificationTypeEnum.EXCHANGE.getType()))
                .and(ownerQualification.status, isEqualTo(1))
                .build()
                .render(RenderingStrategies.MYBATIS3);

        List<OwnerQualification> ownerQualificationList = ownerQualificationMapper.selectMany(selectStatement);
        for (OwnerQualification qualification : ownerQualificationList) {
            SearchExchangeQualificationData qualificationData = ConvertUtil.normalConvert(qualification, SearchExchangeQualificationData.class);
            rows.add(qualificationData);
        }
        return new SearchExchangeQualificationListResponse((long) rows.size(), rows);
    }

    @Override
    public GetExchangeQualificationDetailResponse getExchangeQualificationDetail(Long ownerQualificationId, Integer authType, String authId) {
        //根据ID校验获取以旧换新资质信息
        OwnerQualification exchangeQualification = checkExchangeQualification(ownerQualificationId, authType, authId);
        //获取以旧换新资质详情
        OwnerQualificationDetailDTO ownerQualificationDetail = getOwnerQualificationDetail(Optional.of(exchangeQualification));
        GetExchangeQualificationDetailResponse response = ConvertUtil.normalConvert(ownerQualificationDetail, GetExchangeQualificationDetailResponse.class);
        boolean canReconsider = false;
        //只有【审核拒绝】的资质才允许复核
        if(Objects.equals(response.getApplyStatus(), OwnerQualificationStatusEnum.DENY.getStatus())){
            //若存在有效期，且未过期，则允许复核
            //【有效期】字段会在拒绝的时候，根据政策确认是否赋值
            canReconsider = response.getExpireTime() != null && new Date().before(response.getExpireTime());
        }
        response.setCanReconsider(canReconsider);
        return response;
    }

    @Override
    public void reconsiderExchangeQualification(ReconsiderExchangeQualificationDTO reconsiderExchangeQualificationDTO) {
        //根据ID校验获取以旧换新资质信息


        //校验当前状态能否进行复核

        //TODO
    }

    @Override
    public void cancelReconsiderExchangeQualification(CancelReconsiderExchangeQualificationDTO cancelReconsiderExchangeQualificationDTO) {
        //根据ID校验获取以旧换新资质信息
        OwnerQualification exchangeQualification = checkExchangeQualification(cancelReconsiderExchangeQualificationDTO.getOwnerQualificationId()
                , cancelReconsiderExchangeQualificationDTO.getAuthType(), cancelReconsiderExchangeQualificationDTO.getAuthId());
        //校验当前状态能否取消复核
        if(Objects.equals(exchangeQualification.getApplyStatus(), OwnerQualificationStatusEnum.DENY.getStatus())
            && exchangeQualification.getExpireTime() != null){
            Date now =new Date();
            //更新用户购车资格有效期，提前结束
            UpdateStatementProvider updateStatement = SqlBuilder.update(ownerQualification)
                    .set(ownerQualification.expireTime).equalTo(now)
                    .set(ownerQualification.updatedUserId).equalTo(cancelReconsiderExchangeQualificationDTO.getOperatorId())
                    .set(ownerQualification.updatedUserName).equalTo(cancelReconsiderExchangeQualificationDTO.getOperatorName())
                    .set(ownerQualification.updatedTime).equalTo(now)
                    .where(ownerQualification.id, isEqualTo(cancelReconsiderExchangeQualificationDTO.getOwnerQualificationId()))
                    .and(ownerQualification.expireTime, isNotNull())
                    .and(ownerQualification.expireTime, isGreaterThan(now))
                    .and(ownerQualification.applyStatus, isEqualTo(OwnerQualificationStatusEnum.DENY.getStatus()))
                    .build()
                    .render(RenderingStrategies.MYBATIS3);
            ownerQualificationMapper.update(updateStatement);

            //保存操作日志
            SaveOperateLogDTO saveOperateLogDTO = SaveOperateLogDTO.builder()
                    .userOperateTypeEnum(UserOperateTypeEnum.CANCEL_RECONSIDER_OWNER_QUALIFICATION)
                    .ownerQualificationId(cancelReconsiderExchangeQualificationDTO.getOwnerQualificationId())
                    .operateUserId(cancelReconsiderExchangeQualificationDTO.getOperatorId())
                    .operateUserName(cancelReconsiderExchangeQualificationDTO.getOperatorName())
                    .build();
            userOperateLogService.saveOperateLog(saveOperateLogDTO);
        }
    }

    @Override
    public void reconsiderExchangeFile(ReconsiderFileDTO reconsiderFileDTO) {
        if(reconsiderFileDTO.getReconsiderationFileCopy().isEmpty()){
            throw new ServiceException("请上传以旧换新证明材料");
        }

        OwnerQualification exchangeQualification = checkExchangeQualification(reconsiderFileDTO.getId());
        if(!Objects.equals(exchangeQualification.getApplyStatus(), OwnerQualificationStatusEnum.APPROVE.getStatus())){
            throw new ServiceException("以旧换新预审未通过，暂无法提交证明材料");
        }

        //判断材料是否已经过期
        if(exchangeQualification.getExpireTime() != null && new Date().after(exchangeQualification.getExpireTime())){
            throw new ServiceException("当前以旧换新资质已过期，请重新申请");
        }

        if(!Objects.equals(exchangeQualification.getExchangeStatus(), ExchangeStatusEnum.UNCOMMITTED.getStatus())
                && !Objects.equals(exchangeQualification.getExchangeStatus(), ExchangeStatusEnum.DENY.getStatus())){
            throw new ServiceException("当前状态暂无法提交证明材料，请刷新页面后重试");
        }


        //清空之前上传的图片
        fileService.clearFile(FileTypeEnum.EXCHANGE_PROOF_COPY, exchangeQualification.getId(), reconsiderFileDTO.getOperatorId(), reconsiderFileDTO.getOperatorName());
        //保存上传的文件
        List<String> collect = reconsiderFileDTO.getReconsiderationFileCopy().stream().map(FileInfoDTO::getRelativeFilePath).collect(Collectors.toList());
        fileService.saveFileInfo(FileTypeEnum.EXCHANGE_PROOF_COPY, collect, exchangeQualification.getId(), reconsiderFileDTO.getOperatorId(), reconsiderFileDTO.getOperatorName());

        //更新材料状态为待审核
        Date now = new Date();
        UpdateStatementProvider updateStatement = SqlBuilder.update(ownerQualification)
                .set(ownerQualification.exchangeStatus).equalTo(ExchangeStatusEnum.REVIEWING.getStatus())
                .set(ownerQualification.updatedTime).equalTo(now)
                .set(ownerQualification.updatedUserId).equalTo(reconsiderFileDTO.getOperatorId())
                .set(ownerQualification.updatedUserName).equalTo(reconsiderFileDTO.getOperatorName())
                .set(ownerQualification.exchangeReconsiderationOrg).equalTo(reconsiderFileDTO.getReconsiderOrgName())
                .set(ownerQualification.exchangeApplyTime).equalTo(now)
                .where(ownerQualification.id, isEqualTo(exchangeQualification.getId()))
                .and(ownerQualification.exchangeStatus, isIn(ExchangeStatusEnum.UNCOMMITTED.getStatus(), ExchangeStatusEnum.DENY.getStatus()))
                .and(ownerQualification.applyStatus, isEqualTo(OwnerQualificationStatusEnum.APPROVE.getStatus()))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        ownerQualificationMapper.update(updateStatement);

        //保存操作日志
        SaveOperateLogDTO saveOperateLogDTO = SaveOperateLogDTO.builder()
                .userOperateTypeEnum(UserOperateTypeEnum.RECONSIDER_EXCHANGE_PROOF)
                .ownerQualificationId(exchangeQualification.getId())
                .operateUserId(reconsiderFileDTO.getOperatorId())
                .operateUserName(reconsiderFileDTO.getOperatorName())
                .build();
        userOperateLogService.saveOperateLog(saveOperateLogDTO);
    }


    @Override
    public void approveExchangeQualification(ApproveOwnerQualificationDTO approveOwnerQualificationDTO) {
        OwnerQualification exchangeQualification = checkExchangeQualification(approveOwnerQualificationDTO.getId());
        if(!Objects.equals(exchangeQualification.getApplyStatus(), OwnerQualificationStatusEnum.APPROVE.getStatus())){
            throw new ServiceException("以旧换新预审未通过，暂无法提交证明材料");
        }

        if(!Objects.equals(exchangeQualification.getExchangeStatus(), ExchangeStatusEnum.REVIEWING.getStatus())){
            throw new ServiceException("当前状态暂无法审核明材料，请刷新页面后重试");
        }

        //更新意向用户状态
        Date now = new Date();
        OwnerQualification approveOwnerQualification = new OwnerQualification();
        approveOwnerQualification.setId(exchangeQualification.getId());
        approveOwnerQualification.setExchangeStatus(ExchangeStatusEnum.APPROVE.getStatus());
        approveOwnerQualification.setExchangeReviewTime(now);
        approveOwnerQualification.setExchangeReason(StringUtils.EMPTY);
        approveOwnerQualification.setUpdatedTime(now);
        approveOwnerQualification.setUpdatedUserId(approveOwnerQualificationDTO.getOperatorId());
        approveOwnerQualification.setUpdatedUserName(approveOwnerQualificationDTO.getOperatorName());
        ownerQualificationMapper.updateByPrimaryKeySelective(approveOwnerQualification);

        //保存操作日志
        SaveOperateLogDTO saveOperateLogDTO = SaveOperateLogDTO.builder()
                .userOperateTypeEnum(UserOperateTypeEnum.APPROVE_EXCHANGE_QUALIFICATION)
                .ownerQualificationId(exchangeQualification.getId())
                .operateUserId(approveOwnerQualificationDTO.getOperatorId())
                .operateUserName(approveOwnerQualificationDTO.getOperatorName())
                .build();
        userOperateLogService.saveOperateLog(saveOperateLogDTO);
    }

    @Override
    public void denyExchangeQualification(DenyOwnerQualificationDTO denyOwnerQualificationDTO) {
        if(StringUtils.isBlank(denyOwnerQualificationDTO.getReason())){
            throw new ServiceException("请输入拒绝原因");
        }

        OwnerQualification exchangeQualification = checkExchangeQualification(denyOwnerQualificationDTO.getId());
        if(!Objects.equals(exchangeQualification.getApplyStatus(), OwnerQualificationStatusEnum.APPROVE.getStatus())){
            throw new ServiceException("以旧换新预审未通过，暂无法提交证明材料");
        }

        if(!Objects.equals(exchangeQualification.getExchangeStatus(), ExchangeStatusEnum.REVIEWING.getStatus())){
            throw new ServiceException("当前状态暂无法审核明材料，请刷新页面后重试");
        }

        //更新意向用户状态
        Date now = new Date();
        OwnerQualification approveOwnerQualification = new OwnerQualification();
        approveOwnerQualification.setId(exchangeQualification.getId());
        approveOwnerQualification.setExchangeStatus(ExchangeStatusEnum.DENY.getStatus());
        approveOwnerQualification.setExchangeReviewTime(now);
        approveOwnerQualification.setExchangeReason(denyOwnerQualificationDTO.getReason());
        approveOwnerQualification.setUpdatedTime(now);
        approveOwnerQualification.setUpdatedUserId(denyOwnerQualificationDTO.getOperatorId());
        approveOwnerQualification.setUpdatedUserName(denyOwnerQualificationDTO.getOperatorName());
        ownerQualificationMapper.updateByPrimaryKeySelective(approveOwnerQualification);

        //保存操作日志
        SaveOperateLogDTO saveOperateLogDTO = SaveOperateLogDTO.builder()
                .userOperateTypeEnum(UserOperateTypeEnum.DENY_EXCHANGE_QUALIFICATION)
                .ownerQualificationId(exchangeQualification.getId())
                .operateUserId(denyOwnerQualificationDTO.getOperatorId())
                .operateUserName(denyOwnerQualificationDTO.getOperatorName())
                .build();
        userOperateLogService.saveOperateLog(saveOperateLogDTO);
    }

    @Override
    public OwnerQualification queryAvailableExchangeQualification(Integer authKind, String authId) {
        if(authKind == null | StringUtils.isBlank(authId)){
            return null;
        }
        //查询第一条满足条件的以旧换新资质（可能存在多条）
        SelectStatementProvider selectStatement = select(ownerQualification.allColumns())
                .from(ownerQualification)
                .where(ownerQualification.qualificationType, isEqualTo(OwnerQualificationTypeEnum.EXCHANGE.getType()))
                .and(status, isEqualTo(1))
                .and(ownerQualification.authId, isEqualTo(authId))
                .and(ownerQualification.authType,isEqualTo(authKind))
                .and(ownerQualification.exchangeBindVin, isNull())
                .and(ownerQualification.exchangeStatus, isEqualTo(ExchangeStatusEnum.APPROVE.getStatus()))
                .and(ownerQualification.applyStatus, isEqualTo(OwnerQualificationStatusEnum.APPROVE.getStatus()))
                .limit(1)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return ownerQualificationMapper.selectOne(selectStatement).orElse(null);
    }

    @Override
    public void bindExchangeQualificationVin(String vin, Long id) {
        OwnerQualification updateOwnerQualification = new OwnerQualification();
        updateOwnerQualification.setId(id);
        updateOwnerQualification.setExchangeBindVin(vin);
        updateOwnerQualification.setUpdatedTime(new Date());
        updateOwnerQualification.setUpdatedUserId("-1");
        updateOwnerQualification.setUpdatedUserName("system");
        ownerQualificationMapper.updateByPrimaryKeySelective(updateOwnerQualification);
    }

    @Override
    public void unbindExchangeQualificationVin(Long id) {
        if(id == null){
            return;
        }
        UpdateStatementProvider updateStatement = update(ownerQualification)
                .set(ownerQualification.exchangeBindVin).equalToNull()
                .where(ownerQualification.id, isEqualTo(id))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        ownerQualificationMapper.update(updateStatement);
    }

    @Override
    public void invalidExchangeQualification(InvalidOwnerQualificationDTO invalidOwnerQualificationDTO) {
        OwnerQualification exchangeQualification = ownerQualificationMapper.selectByPrimaryKey(invalidOwnerQualificationDTO.getId()).orElse(null);
        if (exchangeQualification == null) {
            throw new ServiceException("未查询到相应用户信息");
        }
        if (exchangeQualification.getExpireTime() == null) {
            throw new ServiceException("该数据无法失效");
        }
        Date now = new Date();
        if (exchangeQualification.getExpireTime().compareTo(now) <= 0) {
            throw new ServiceException("该资格信息已过期，无需失效");
        }

        if(!Objects.equals(OwnerQualificationTypeEnum.EXCHANGE.getType(), exchangeQualification.getQualificationType())){
            throw new ServiceException("资格类型异常");
        }

        if(StringUtils.isNotBlank(exchangeQualification.getExchangeBindVin())){
            throw new ServiceException("该以旧换新资格已被使用，无法失效");
        }


        //更新用户购车资格有效期，提前结束
        UpdateStatementProvider updateStatement = SqlBuilder.update(ownerQualification)
                .set(ownerQualification.expireTime).equalTo(now)
                .set(ownerQualification.updatedUserId).equalTo(invalidOwnerQualificationDTO.getOperatorId())
                .set(ownerQualification.updatedUserName).equalTo(invalidOwnerQualificationDTO.getOperatorName())
                .set(ownerQualification.updatedTime).equalTo(now)
                .where(ownerQualification.id, isEqualTo(invalidOwnerQualificationDTO.getId()))
                .and(ownerQualification.expireTime, isNotNull())
                .and(ownerQualification.expireTime, isGreaterThan(now))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        ownerQualificationMapper.update(updateStatement);

        //保存操作日志
        SaveOperateLogDTO saveOperateLogDTO = SaveOperateLogDTO.builder()
                .userOperateTypeEnum(UserOperateTypeEnum.INVALID_OWNER_QUALIFICATION)
                .reason(invalidOwnerQualificationDTO.getReason())
                .ownerQualificationId(invalidOwnerQualificationDTO.getId())
                .operateUserId(invalidOwnerQualificationDTO.getOperatorId())
                .operateUserName(invalidOwnerQualificationDTO.getOperatorName())
                .build();
        userOperateLogService.saveOperateLog(saveOperateLogDTO);
    }

    /**
     * 根据车架号判断是否已经录入以旧换新信息
     * @param excludeId 排除的ID（自身)
     * @param authType 证件类型
     * @param authId 证件号
     * @param exchangeVin 以旧换新的车架号
     * @return true:已经存在以旧换新的记录  false:不存在以旧换新的记录
     */
    private boolean existExchangeQualification(@Nullable  Long excludeId, Integer authType, String authId, String exchangeVin){
        SelectStatementProvider selectStatement = select(ownerQualification.allColumns())
                .from(ownerQualification)
                .where(ownerQualification.authType, isEqualTo(authType))
                .and(ownerQualification.authId, isEqualTo(authId))
                .and(ownerQualification.exchangeVin, isEqualTo(exchangeVin))
                .and(status, isEqualTo(1))
                //剔除本身的记录
                .and(ownerQualification.id , isNotEqualToWhenPresent(excludeId))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        List<OwnerQualification> ownerQualificationList = ownerQualificationMapper.selectMany(selectStatement);
        return !ownerQualificationList.isEmpty();
    }

    /**
     * 验证以旧换新资格信息
     * @param ownerQualificationId 以旧换新资质ID
     * @return 满足条件的以旧换新资格-若不满足条件，则抛出异常
     */
    private OwnerQualification checkExchangeQualification(Long ownerQualificationId){
        if(ownerQualificationId == null){
            throw new ServiceException("未查询到相关以旧换新资格信息");
        }
        OwnerQualification exchangeQualification = ownerQualificationMapper.selectByPrimaryKey(ownerQualificationId).orElse(null);
        if(exchangeQualification == null || exchangeQualification.getStatus() == 0){
            throw new ServiceException("未查询到相关以旧换新资格信息，请刷新页面重试");
        }
        if(!Objects.equals(OwnerQualificationTypeEnum.EXCHANGE.getType(), exchangeQualification.getQualificationType())){
            throw new ServiceException("资格类型异常");
        }
        return exchangeQualification;
    }

    /**
     * 验证以旧换新资格信息
     * @param ownerQualificationId 以旧换新资质ID
     * @param authType 提交人证件类型
     * @param authId 提交人证件号
     * @return 满足条件的以旧换新资格-若不满足条件，则抛出异常
     */
    private OwnerQualification checkExchangeQualification(Long ownerQualificationId, Integer authType, String authId){
        OwnerQualification exchangeQualification = checkExchangeQualification(ownerQualificationId);
        if(!Objects.equals(authType, exchangeQualification.getAuthType()) || !Objects.equals(authId, exchangeQualification.getAuthId())){
            throw new ServiceException("您暂无权限查询该条信息，请联系管理员");
        }
        return exchangeQualification;
    }

}
